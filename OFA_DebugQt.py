import inspect
import sys
from threading import get_ident

from PySide6 import QtWidgets, QtGui

method_descriptor = type(QtWidgets.QWidget.windowTitle)
function = type(QtWidgets.QWidget.__dir__)
salida = sys.stdout

# ? redirected, notify, event, paintEvent
exlude = {'redirected', 'notify', 'event', 'paintEvent', 'emit', 'currentData', 'isChecked', 'currentItem', 'parent', 'count', 'itemData', 'findData', 'itemText'}


def install():
    threadID = get_ident()
    for qt in (QtGui, QtWidgets):
        for w in dir(qt):
            clazz = getattr(qt, w)
            for i in dir(clazz):
                if i not in exlude:
                    funt = getattr(clazz, i)
                    if isinstance(funt, (method_descriptor, function)):
                        def hoot(*args, __funtName__=i, __funt__=funt, __clazz__=clazz, __threadID__=threadID, **kwargs):
                            threadID = get_ident()
                            if __threadID__ != threadID:
                                print(f'{inspect.currentframe().f_back}: {__clazz__}.{__funtName__}, {threadID}')
                            return __funt__(*args, **kwargs)

                        if hasattr(funt, 'connect'):
                            hoot.connect = funt.connect
                        setattr(clazz, i, hoot)
                    # else:
                        # print(i, type(funt))


if __name__ == '__main__':
    install()
    qApp = QtWidgets.QApplication(sys.argv)
    ventana = QtWidgets.QWidget()
    ventana.show()
    qApp.exec()
