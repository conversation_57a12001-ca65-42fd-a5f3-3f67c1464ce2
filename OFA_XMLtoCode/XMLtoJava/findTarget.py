from _Filtros import _getFiltros


def findTarget(newClass, x, i):
    <PERSON>ltro, Comentario = _getFiltros(x, i, "object")
    newClass += f'        if this.findTarget(EAIFindTargetType.{(x.get("target")) if x.get("target") else "Enemy"}, ' \
                f'EAIFindType.normal, {x.get("ally") if x.get("ally") else "false"}, object ->{Filtro})\n' \
                f'            if this.changeState(onDoActionEnd -> self.{x.get("state")}(0.3))\n' \
                f'                return true;\n'
    return newClass