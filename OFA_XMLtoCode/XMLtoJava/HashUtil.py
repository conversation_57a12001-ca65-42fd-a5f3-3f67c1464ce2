import jpype


class HashUtil:
    HashUtilJava = jpype.JClass("BDOCrypt.HashUtil")

    @classmethod
    def generateHashA(cls, actionName: str) -> int:
        return int(cls.HashUtilJava.generateHash(actionName, 1))

    @classmethod
    def generateHashC(cls, actionName: str, charset: str) -> int:
        if 'utf' in charset:
            actionName = actionName.encode(charset)[2:]
        else:
            actionName = actionName.encode(charset)
        return int(cls.HashUtilJava.generateHash(actionName, 3))
