def sender_info(newClass, i):
    if i.get("info").lower() == 'target':
        newClass += f'        this.getActor().getAggroList().addCreature(this.getTarget());\n'
    else:
        print('Falta 1', i.tag, i.get("info"))

    if i.get("state") is not None:
        newClass += f'        if this.changeState(onDoActionEnd -> this.{i.get("state")}(0.1))\n' \
                    f'            return EAiHandlerResult.CHANGE_STATE;\n'

    return newClass
