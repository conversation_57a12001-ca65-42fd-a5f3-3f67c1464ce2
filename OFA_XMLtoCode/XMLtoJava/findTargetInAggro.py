from _Filtros import _getFiltros


def findTargetInAggro(newClass, x, i):
    <PERSON>lt<PERSON>, Comentario = _getFiltros(x, i, "object")
    newClass += f'        if this.findTargetInAggro(EAIFindTargetType.Enemy, EAIFindType.normal, object ->{Filtro})\n' \
                f'            if this.changeState(onDoActionEnd -> self.{x.get("state")}(0.3))\n' \
                f'                return;\n'
    return newClass