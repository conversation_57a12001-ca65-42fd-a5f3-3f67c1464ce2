from HashUtil import <PERSON>h<PERSON><PERSON>
from NumberUtils import isCreatable


def variable(newClass, x):
    Hash = HashUtil.generateHashA(x.get("name").lower())
    value = x.get("value", "0")
    if not isCreatable(value):
        value = HashUtil.generateHashA(value.lower())
        value = f'this.getVariable({value} /*{x.get("value", "0")}*/)'
    newClass += f'        this.setVariable({Hash} /*{x.get("name")}*/, {value});\n'
    return newClass
