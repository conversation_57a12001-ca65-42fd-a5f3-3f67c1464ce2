def teleport(newClass, x):
    if not x.get("waypoint"):
        getDest = x.get("dest", "Random")
        if getDest.lower() == 'owner_position':
            newClass += f'        this.doTeleport(EAIMoveDestType.OwnerPosition, {x.get("OffsetX")}, {x.get("OffsetY")}, {x.get("min", 0)}, {x.get("max", 0)});\n'
        else:
            newClass += f'        this.doTeleport(EAIMoveDestType.{getDest[0].upper()+getDest[1:]}, {x.get("OffsetX")}, {x.get("OffsetY")}, {x.get("min", 0)}, {x.get("max", 0)});\n'
    else:
        newClass += f'        this.doTeleportToWaypoint("{x.get("dest")}", "{x.get("waypoint")}", {x.get("OffsetX")}, {x.get("OffsetY")}, {x.get("min", 0)}, {x.get("max", 0)});\n'
    return newClass
