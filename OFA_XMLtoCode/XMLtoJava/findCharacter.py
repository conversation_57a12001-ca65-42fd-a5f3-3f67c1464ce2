from _Filtros import _getFiltros


def findCharacter(newClass, x, i):
    Filtro, Coment = _getFiltros(x, i, "object")
    newClass += f'        if this.findTarget(EAIFindTargetType.{x.get("target")[0].upper() + x.get("target")[1:]}, ' \
                f'EAIFindType.normal, {x.get("ally")[0].upper() + x.get("ally")[1:]}, lambda object:{Filtro})\n' \
                f'            if self.changeState(lambda: self.{x.get("state")}(0.3))\n' \
                f'                return;\n'
    return newClass
