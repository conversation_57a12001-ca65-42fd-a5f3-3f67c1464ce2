from HashUtil import HashUtil
from _Filtros import _getFiltros


def findcharactercount(newClass, x, i):
    Filtro, Comentario = _getFiltros(x, i, 'object')
    # if Filtro:
    #     newClass += f'        if {Filtro}:{Comentario}\n'
    Hash = HashUtil.generateHashA(x.get("dest").lower())
    type = x.get("target", "Monster")
    newClass += f'        this.setVariable({Hash} /*{x.get("dest")}*/, this.findCharacterCount(EAIFindTargetType.{type[0].upper()+type[1:]}, {x.get("ally")}, object -> {x.get("min")} >= this.getDistanceTo(object) <= {x.get("max")}{" && "+Filtro if Filtro else ""}));\n'

    return newClass
