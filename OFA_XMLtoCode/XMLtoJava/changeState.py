from HashUtil import Hash<PERSON>til
from NumberUtils import isCreatable

from _Filtros import _getFiltros
from _checkName import _chechName


def changeState(newClass, x, i, endReturn = ' true'):
    Filtro, Comentario = _getFiltros(x, i, 'target')
    if Filtro:
        newClass += f'        if {Filtro}\n'
    altIf = ''
    if x.get("dice") is not None:
        value = x.get("dice")
        if not isCreatable(value):
            value = HashUtil.generateHashA(value.lower())
            value = f'this.getVariable({value}L /*{x.get("dice")}*/)'
        altIf += f'Rnd.getChance({value}) && '
    if x.get("callcount") is not None:
        altIf += f'this.getCallCount() == {x.get("callcount")} && '
    if altIf:
        newClass += f'{("    " if Filtro else "")}        if {altIf[:-4]}\n'
    newClass += f'{("    "if Filtro else "")+("    "if altIf else "")}        if this.changeState(onDoActionEnd -> this.{_chechName(x.get("state"))}({x.get("blendTime", "0.3")}))\n' \
                f'{("    "if Filtro else "")+("    "if altIf else "")}            return {endReturn};\n'
    return newClass