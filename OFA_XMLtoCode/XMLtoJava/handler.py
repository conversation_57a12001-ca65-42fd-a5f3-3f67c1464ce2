from HashUtil import <PERSON>h<PERSON><PERSON>

from sender_info import sender_info
from _Filtros import _getFiltros
from _checkName import _chechName
from compute import compute
from changeState import changeState
from send_command import send_command


def handler(newClass, i):
    newClass += f"    public EAiHandlerResult {_chechName(i.get('name'))}(Creature sender, Integer[] eventData) {{\n" \
                f"        this._sender = sender;\n"
    for x in i.findall('*'):
        if x.tag == 'changeState':
            newClass = changeState(newClass, x, i, 'EAiHandlerResult.CHANGE_STATE')
        elif x.tag == 'getEventValue':
            Hash4 = HashUtil.generateHashA(x.get("name").lower())
            newClass += f'        this.setVariable({Hash4} /*{x.get("name")}*/, eventData[{x.get("index")}]);\n'
        elif x.tag == 'getTargetFromSenderInfo':
            Filtro, Comentario = _getFiltros(x, i, 'sender')
            if not Filtro:
                newClass += f'        this.getActor().getAggroList().addCreature(sender.getAggroList().getTarget())\n' \
                            f'        if this.changeState(state -> this.{_chechName(x.get("state"))}({x.get("blendTime", "0.3")})):\n' \
                            f'            return EAiHandlerResult.CHANGE_STATE;\n'
            else:
                newClass += f'        if {Filtro}:{Comentario}\n' \
                            f'            this.getActor().getAggroList().addCreature(sender.getAggroList().getTarget())\n' \
                            f'            if this.changeState(state -> this.{_chechName(x.get("state"))}({x.get("blendTime", "0.3")})):\n' \
                            f'                return EAiHandlerResult.CHANGE_STATE;\n'
        elif x.tag == 'compute':
            newClass = compute(newClass, x)
        elif x.tag == 'send_command':
            newClass = send_command(newClass, x, i)
        elif x.tag == 'sender_info':
            newClass = sender_info(newClass, x)
        else:
            print('Falta handler 1:', i.get("name"), x.tag)
    newClass += f'        return EAiHandlerResult.BYPASS;\n'
    newClass += '    }\n'
    return newClass
