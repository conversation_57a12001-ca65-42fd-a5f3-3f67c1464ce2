def summonCharacters(newClass, x):
    newClass += f'        this.summonCharacters({x.get("characterKey")}, {x.get("ActionIndex", 0)}, {x.get("offsetX", 0)}, {x.get("offsetZ", 0)}, {x.get("offsetY", 0)}, {x.get("isChild")},' \
                f' {x.get("RandomIntervalX", 0)}, {x.get("RandomIntervalZ", 0)}, {x.get("RandomNumberRangeX", 0)}, {x.get("RandomNumberRangeZ", 0)}, {x.get("MinSummonCount", 1)}, {x.get("MaxSummonCount", 1)});\n'
    return newClass