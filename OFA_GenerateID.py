import random

__autors__ = ['Kirito']

baloresPermitidos = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
baloresPermitidos = list(set(baloresPermitidos))
baloresPermitidos.sort()
baloresPermitidos = "".join(baloresPermitidos)

numtochar = {}
chartonum = {}

for num, char in enumerate(baloresPermitidos):
    numtochar.update({num + 1: char})
    chartonum.update({char: num + 1})

maxzice = len(baloresPermitidos)


def GenerarID(num):
    ID = []
    while num > 0:
        x = num % maxzice
        if x:
            ID.append(numtochar[x])
            num = num // maxzice
        else:
            ID.append(numtochar[maxzice])
            num = (num // maxzice) - 1
    return "".join(ID[::-1])


def OptenerNum(ID):
    num = 0
    for i in ID:
        num = (num * maxzice) + chartonum[i]
    return num


def generateRandomID(cize:int, seed=None) -> str:
    assert cize > 0
    min = OptenerNum(baloresPermitidos[-1]*(cize-1))+1
    max = OptenerNum(baloresPermitidos[-1]*cize)
    random.seed(seed)
    randNum = random.randint(min, max)
    return GenerarID(randNum)

if __name__ == '__main__':
    print(generateRandomID(10, 'Conker'))
#     for i in range(99999):
#         print(generateRandomID(3))
#     print(hex(221919451578090))
#     # print(baloresPermitidos[-1]*8)
#     print(OptenerNum(baloresPermitidos[-1] * 8))
#
#     # print("Z", OptenerNum("Z"), GenerarID(OptenerNum("Z")))
#     print(GenerarID(221919451578090))
#     text = baloresPermitidos + baloresPermitidos[0] + baloresPermitidos[-1] + baloresPermitidos[::-1]
#     # print(len(baloresPermitidos))
#     for i in text:
#         for t in text:
#             t = i + t
#             if t != GenerarID(OptenerNum(t)):
#                 print(t, OptenerNum(t), GenerarID(OptenerNum(t)))
#         if i != GenerarID(OptenerNum(i)):
#             print(i, OptenerNum(i), GenerarID(OptenerNum(i)))
#
#     if text != GenerarID(OptenerNum(text)):
#         print("", text,
#               OptenerNum(text), "\n",
#               GenerarID(OptenerNum(text)))
#     print("ok")
#
#     # print("0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"[::-1])
#     print(GenerarID(OptenerNum(text)))
#     print(OptenerNum(text))
#     # print(OptenerNum("0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZZYXWVUTSRQPONMLKJIHGFEDCBAzyxwvutsrqponmlkjihgfedcba9876543210"))
#     from traceback import print_exc
#
#     print(GenerarID(3004142822311961681685446617322))
#     while True:
#         a = randint(1, 3004142822311961681685446617322)
#         try:
#             x = y = None
#             x = GenerarID(a)
#             y = OptenerNum(x)
#             if y != a:
#                 print(a, x, y)
#                 raise ("no coinciden")
#         except:
#             print_exc()
#             print(a)
#             print(x)
#             print(y)
#             break

