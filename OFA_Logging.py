import linecache
import logging
import re
import sys
import traceback
from cProfile import Profile as _Profile
from contextlib import ContextDecorator
from datetime import datetime
from io import StringIO
from logging import info, warn, warning, error, getLogger, exception, debug

from os import mkdir
import threading
from time import perf_counter
try:import tracemalloc
except:tracemalloc = None

__all__ = ["logging", "info", "warn", "warning", "error", "exception", "getLogger", "Profile", "debug"]

file = rootFolder = funcReport = nowLevel = None

lockConsole = threading.Lock()

printConsole: type = None
ConsolaDeleteCodes = re.compile(b'(\x1b\[[^a-zA-Z]*.)')
showInConsole = False

class Profile(_Profile):
    def __enter__(self):
        self.enable()

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.disable()
        self.print_stats(2)

        if exc_type and exc_val and exc_tb:
            my_exchandler(exc_type, exc_val, exc_tb, self.globales, self.locales)
        return True

def writeFile(text, tipe):
    if rootFolder and nowLevel != logging.NOTSET:
        try:
            open(rootFolder+'/'+tipe+'_'+file, "ab").write(ConsolaDeleteCodes.sub(b'', text.encode()) + b'\n')
        except:
            if printConsole:
                printConsole(sys.stderr, traceback.format_exc())


class stdinLog(StringIO):
    _stdin = sys.stdin
    acumular = ''

    def write(self, text):
        text = str(text)
        with lockConsole:
            if not text.endswith('\n'):
                self.acumular += text
                return
            text = self.acumular + text
            self.acumular = ''
            if printConsole:
                printConsole(sys.stdin, text)
            if showInConsole or (nowLevel == logging.NOTSET and stdinLog._stdin):
                stdinLog._stdin.write(text)
            writeFile(text, 'Input-')


class stderrLog(StringIO):
    _stderr = sys.stderr
    acumular = ''

    def write(self, text):
        text = str(text)
        with lockConsole:
            if not text.endswith('\n'):
                self.acumular += text
                return
            text = self.acumular + text
            self.acumular = ''
            if printConsole:
                printConsole(sys.stderr, text)
            if showInConsole or (nowLevel == logging.NOTSET and stderrLog._stderr):
                stderrLog._stderr.write(text)
            writeFile(text, 'Error-')


class stdoutLog(StringIO):
    _stdout = sys.stdout
    acumular = ''

    def write(self, text):
        text = str(text)
        with lockConsole:
            if not text.endswith('\n'):
                self.acumular += text
                return
            text = self.acumular + text
            self.acumular = ''

            if printConsole:
                printConsole(sys.stdout, text)
            if showInConsole or (nowLevel == logging.NOTSET and stdoutLog._stdout):
                stdoutLog._stdout.write(text)
            writeFile(text, 'Salida-')


def my_exchandler(type, value, tb, *, globales=False, locales=False):
    global funcReport
    traceback.print_exception(type, value, tb)
    if funcReport:
        msg = ""
        try:
            for line in traceback.format_exception(type, value, tb):
                msg += line
        except:msg = f'ERROR no se pudo mostrar mas detalles: {(type, value, tb)}\n'

        locals = True
        if tb:
            for active_vars in [tb.tb_frame.f_locals, tb.tb_frame.f_globals]:
                if locals and locales:
                    header = 'Locals:'
                elif locals and not locales:
                    locals = False
                    continue
                if not locals and globales:
                    header = 'Globals:'
                elif not locals and not globales:
                    break
                msg += header + "\n"
                for k, v in active_vars.items():
                    if not (k.startswith('__') and k.endswith('__')):
                        msg += '\t{} = {}\n'.format(k, v)
                locals = False
        try:
            funcReport(msg, type, value, tb)
        except Exception as e:
            funcReport = None
            raise e


class checkErrors(ContextDecorator):
    def __init__(self, globales=True, locales=True):
        self.locales = locales
        self.globales = globales

    def __enter__(self):
        return self

    def __exit__(self, type, value, tb):
        if type and value and tb:
            my_exchandler(type, value, tb, self.globales, self.locales)
        return True


class getState(ContextDecorator):
    def __init__(self, Debug=True, file="", showError=True, writeOnlyError=False, limit=10):
        super().__init__()
        self.writeOnlyError = writeOnlyError
        self.Debug = Debug
        self.showError = showError
        self.limit = limit
        if self.Debug:
            self.time = file or rootFolder

    def __enter__(self):
        if self.Debug and tracemalloc:
            self.perfTime = perf_counter()
            tracemalloc.start()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.Debug and tracemalloc:
            perfTimeT = perf_counter()
            snapshot = tracemalloc.take_snapshot()
            tracemalloc.stop()
            if (self.writeOnlyError and exc_val) or not self.writeOnlyError:
                self.perfTime = perfTimeT - self.perfTime
                self.display_top(snapshot)
                if exc_val and (self.showError or threading.current_thread().name != "MainThread"):
                    locals = True
                    for active_vars in [exc_tb.tb_frame.f_locals, exc_tb.tb_frame.f_globals]:
                        header = 'Locals:' if locals else 'Globals:'
                        print(header, file=sys.stderr)
                        for k, v in active_vars.items():
                            if not (k.startswith('__') and k.endswith('__')):
                                texto = '\t{} = {}'.format(k, v)
                                if len(texto) > 1024:
                                    texto = '\t{} = type: {}'.format(k, v.__class__)
                                print(texto, file=sys.stderr)
                        locals = False
        return True

    def display_top(self, snapshot, key_type='lineno'):
        snapshot = snapshot.filter_traces((
            tracemalloc.Filter(False, "<frozen importlib._bootstrap>"),
            tracemalloc.Filter(False, "<unknown>"),
        ))
        top_stats = snapshot.statistics(key_type)
        file = sys.stderr
        file.write("STATES - " + str(datetime.now()) + "\n")
        file.write("Top %s lines\n" % self.limit)
        for index, stat in enumerate(top_stats[:self.limit], 1):
            frame = stat.traceback[0]
            size = bytes2human(stat.size)
            file.write('#%s: File "%s", line %i: %s %s\n'
                       % (index, frame.filename, frame.lineno, size[0], size[1]))
            line = linecache.getline(frame.filename, frame.lineno).strip()
            if line:
                file.write('    %s\n' % line)

        other = top_stats[self.limit:]
        if other:
            size = bytes2human(sum(stat.size for stat in other))
            file.write("%s other: %s %s\n" % (len(other), size[0], size[1]))
        total = bytes2human(sum(stat.size for stat in top_stats))
        file.write("Total allocated size: %s %s\n" % (total[0], total[1]))
        file.write("Tiempo de ejecucion: %s\n" % self.perfTime)


def bytes2human(Bytes, redonde=3, suffix="B"):
    """
    Scale bytes to its proper format
    e.g:
        1253656 => '1.20MB'
        1253656678 => '1.17GB'
    """
    factor = 1024
    for unit in ("", "K", "M", "G", "T", "P", "E", "Z", "Y"):
        if Bytes < factor:
            return round(Bytes, redonde), f"{unit}{suffix}"
        Bytes /= factor


def init(carpeta: str = "", handlers=None, report=None, showProcess=False, processName=None, level=logging.INFO, showOuput=False):
    """"
    '[%(asctime)s,%(msecs)d]',datefmt='%d. %m %Y %H:%M:%S' : Tiempo
    [%(relativeCreated)d] : Tiempo de ejecucion del programa
    %(module)s : Modulo en el que se ejecuto
    %(funcName)s : Funcion en el que se ejecuto
    %(threadName)s : Hilo en el que se ejecuto
    %(processName)s : Proceso en el que se ejecuto
    %(name)s : Nombre del logging actual
    """
    global nowLevel, rootFolder, funcReport, file, showInConsole
    showInConsole = showOuput
    nowLevel = level
    # from OFA_Colorama import coloredlogs
    proces = "\033[4m%(processName)s\033[0m, "
    # coloredlogs.install(milliseconds=True, datefmt='%H:%M:%S',
    #                     fmt=f'\033[95m%(asctime)s.%(msecs)d\033[0m [{proces if showProcess else (processName + ", " if processName else "")}\033[4m%(name)s\033[0m] %(levelname)s -> %(message)s\033[0m',
    #                     field_styles={}, level_styles={}, level=level)
    logging.addLevelName(logging.DEBUG, "\033[94mDEBUG")
    logging.addLevelName(logging.ERROR, "\033[31mERROR")
    logging.addLevelName(logging.WARNING, "\033[93mWARNING")
    logging.addLevelName(logging.INFO, "\033[92mINFO")
    funcReport = report
    if carpeta:
        try:mkdir(carpeta)
        except:pass
        rootFolder = carpeta
        dateFile = str(datetime.now()).replace(":", "-")
        file = dateFile[:dateFile.find(".")] + ".log"
    logging.basicConfig(datefmt='%H:%M:%S', level=logging.DEBUG, handlers=handlers)
    # proces = "%(processName)s, "
    # logging.basicConfig(
    #     format=f'%(asctime)s.%(msecs)d [{proces if showProcess else (processName + ", " if processName else "")}%(name)s] %(levelname)s -> %(message)s',
    #     datefmt='%H:%M:%S', level=logging.DEBUG, handlers=handlers
    # )

# if not sys.gettrace():
#     sys.stdin = stdinLog()
#     sys.stdout = stdoutLog()
#     sys.stderr = stderrLog()


if __name__ == '__main__':
    init(level=logging.NOTSET)
    ts = getTimes()
    ts.enable()
    logging.debug('Pronado debug')
    logging.error('Pronado error')
    logging.warning('Pronado warning')
    logging.info('Pronado info')
    ts.disable()
    ts.print_stats(2)
