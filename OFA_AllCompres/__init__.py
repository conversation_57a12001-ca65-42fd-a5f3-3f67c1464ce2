from os import stat

from OFA_Requesting import request
from .FileFTP import <PERSON>FT<PERSON>
from .FileHTTP import <PERSON><PERSON><PERSON>
from .RAR import RAR, RarInfo
from .RAR.rarfile import <PERSON>r<PERSON><PERSON><PERSON><PERSON>n<PERSON>, PipeReader
from .ZIP import ZIP

__version__ = '0.1.0'
__all__ = ["allCompres", "FileHTTP", "FileFTP", "RAR", "ZIP"]


class allCompres:
    file = type = protocol = None

    def __init__(self, path: str, cache=None, tipo=None):
        self.path = path
        head = path.lower()
        protocol = None
        if head.startswith(("http", "https")):
            protocol = FileHTTP
            self.type = request().open(path).headers.get('Content-Type')
        elif head.startswith("ftp"):
            protocol = FileFTP
            if head.endswith('.rar') or tipo == '.rar':
                self.type = "application/x-rar-compressed"
            elif head.endswith('.zip') or tipo == '.zip':
                self.type = ".zip"
        else:
            if head.endswith('.rar') or tipo == '.rar':
                self.type = "application/x-rar-compressed"
            elif head.endswith('.zip') or tipo == '.zip':
                self.type = ".zip"
        if protocol:
            fp = self.protocol = protocol(path, cache=cache)
            # fp = path
        else:
            fp = path.replace('file://', '', 1)
        if self.type == "application/x-rar-compressed" or head[-4:] == ".rar":
            self.file = RAR(fp)
        # elif head[-4:] == ".zip":
        #     self.file = ZIP(fp)
        else:raise Exception(f"Formato no soportado: {self.type}, {path}")
        if isinstance(fp, (FileHTTP, FileFTP)):
            fp.cache = None

    @staticmethod
    def checkURL(url) -> str:
        head = url.lower()
        tipo = None
        if head.endswith('.rar'):
            tipo = "application/x-rar-compressed"
        elif head.endswith('.zip'):
            tipo = ".zip"
        elif head.startswith(("http", "https")):
            tipo = request().open(url).headers.get('Content-Type')
        elif head.startswith("ftp"):
            tipo = request().open(url).headers.get('Content-Type')
        if tipo == "application/x-rar-compressed" or head[-4:].lower() == ".rar":
            return 'RAR'
        elif head[-4:].lower() == ".zip":
            return 'ZIP'

    def size(self):
        if self.protocol:
            return self.protocol.length
        return stat(self.path).st_size

    def setpassword(self, pwd):
        self.file.setpassword(pwd)

    def listDir(self) -> list:
        if isinstance(self.file, ZIP):
            return self.file.NameToInfo.keys()
        elif isinstance(self.file, RAR):
            return self.file.namelist()

    def getDict(self) -> 'dict[str, Rar5FileInfo]':
        if isinstance(self.file, ZIP):
            return self.file.NameToInfo
        elif isinstance(self.file, RAR):
            return self.file._file_parser._info_map

    def getinfo(self, name):
        return self.file.getinfo(name)

    def open(self, file, pwd=None, raw=False) -> PipeReader:
        return self.file.open(file, pwd=pwd, raw=raw)

    def abort(self):
        if isinstance(self.file.fp, (FileHTTP, FileFTP)):
            self.file.fp.abort()
