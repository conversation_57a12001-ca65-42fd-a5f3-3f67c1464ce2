from abc import abstractmethod
from ftplib import FTP, _SSLSocket, error_temp


class FileFTP:
    def __init__(self, file, cache=None):
        self.cache = cache
        self.url = file
        file = file.split("//", 1)[1]
        self.host, self.file = file.split("/", 1)
        self.host = self.host.split("@", 1)
        if len(self.host) == 2:
            user, self.host = self.host
            self.user, self.pwd = user.split(":")
        else:
            self.user, self.pwd = ("", "")
            self.host = self.host[0]
        self.host = self.host.split(":")
        self.ftp = FTP(timeout=30)
        self.ftp.connect(self.host[0], int(self.host[1]) if len(self.host) == 2 else 0)
        self.ftp.encoding = "UTF-8"
        self.ftp.login(self.user, self.pwd)
        self.ftp.voidcmd('TYPE I')
        self.length = self.ftp.size(self.file)
        if not self.length:
            raise ValueError("No se a podido obtener el tamaño del archivo")
        self.pos = 0

    def __len__(self):
        return self.length

    def abort(self):
        self.ftp.close()

    close = abort

    @property
    def mode(self) -> str:
        return 'rb'

    def read(self, size: int = -1) -> bytes:
        start = self.tell()
        stop = start + size if 0 <= size <= self.length - start else self.length
        self.ensure(start, stop - 1)
        if size < 0:
            size = self.length - self.pos
        data = self.retrbinary(self.file, rest=self.pos, blocksize=size)
        self.pos += len(data)
        print(self.pos)
        return data

    def retrbinary(self, cmd, blocksize=0, rest=None):
        if self.cache:
            try:
                data = self.cache.load(self.url, 'FileFTP.read', self.pos, cmd, blocksize, rest)
            except:
                data = None
            if data is not None:
                return data
        self.ftp.voidcmd('TYPE I')
        with self.ftp.transfercmd("RETR " + cmd, rest) as conn:
            data = b""
            while True:
                raw = conn.recv(8192)
                if not raw:
                    break
                data += raw
                if len(data) >= blocksize:
                    if len(data) > blocksize:
                        data = data[:blocksize]
                    break
            if _SSLSocket is not None and isinstance(conn, _SSLSocket):
                conn.unwrap()
        try:
            self.ftp.voidresp()
        except error_temp:
            pass
        if self.cache:
            self.cache.dump(data, self.url, 'FileFTP.read', self.pos, cmd, blocksize, rest)
        return data

    def tell(self) -> int:
        return self.pos

    def seek(self, offset: int, whence: int = 0) -> int:
        pos = 0
        if whence == 2:
            pos = self.length
        elif whence:
            pos = int(self.length / 2)
        self.pos = pos + offset
        return self.pos

    def seekable(self):
        return True

    @abstractmethod
    def ensure(self, start: int, end: int) -> None:
        pass
