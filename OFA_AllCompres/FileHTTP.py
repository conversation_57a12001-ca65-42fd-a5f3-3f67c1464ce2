from abc import ABC, abstractmethod
from typing import IO

from OFA_Cache import Cache
from OFA_Requesting import request


class FileHTTP(ABC, IO[bytes]):
    def __init__(self, url, cache: 'Cache' = None):
        super().__init__()
        self.cache = cache
        self.url = url
        self.request = request()
        self.fp = self.request.open(self.url)
        self.length = int(self.fp.headers["Content-Length"])
        self.Accept_Ranges = self.fp.headers["Accept-Ranges"]
        if not self.length:
            raise ValueError("No se a podido obtener el tamaño del archivo")
        self._pos = self.pos = 0

    def __len__(self):
        return self.length

    def seekable(self):
        return True

    def abort(self):
        self.fp.close()
        self.request.close()

    @property
    def mode(self) -> str:
        return 'rb'

    def read(self, size: int = -1) -> bytes:
        if self.cache:
            try:
                data = self.cache.load(self.url, 'FileHTTP.read', self.pos, size)
            except:
                data = None
            if data is not None:
                self.pos += len(data)
                return data
        pos = self.pos
        if pos == self.length:
            pos -= 1
        headers = [header for header in self.request.addheaders if header[0] == 'Range']
        for header in headers:
            self.request.addheaders.remove(header)
        self.request.add_headers({"Range": f"bytes={pos}-"})
        self.fp.close()
        self.fp = self.request.open(self.url)
        if pos == self.length:
            self.fp.read(1)
        start = self.tell()
        stop = start + size if 0 <= size <= self.length - start else self.length
        self.ensure(start, stop - 1)
        if size < 0:
            size = None
        data = self.fp.read(size)
        if self.cache:
            self.cache.dump(data, self.url, 'FileHTTP.read', self.pos, size)
        self.pos += len(data)
        self._pos = self.pos
        return data

    def seek(self, offset: int, whence: int = 0) -> int:
        pos = 0
        if whence == 2:
            pos = self.length
        elif whence:
            pos = int(self.length / 2)
        self.pos = pos + offset
        return self.pos

    @abstractmethod
    def ensure(self, start: int, end: int) -> None:
        pass

    def truncate(self, size=None) -> int:
        return self.length

    def tell(self) -> int:
        return self.pos
