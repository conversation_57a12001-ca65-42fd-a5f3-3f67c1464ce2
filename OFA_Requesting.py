import http.client
import json
import socket
import ssl
from http.cookiejar import CookieJar
from urllib.error import URLError, HTTPError
from urllib.parse import urlencode
from urllib.request import <PERSON>er<PERSON>ire<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, HTTP<PERSON><PERSON><PERSON>, \
    HTTPDefaultError<PERSON>and<PERSON>, HTTPRedirectHandler, FTPHandler, FileHandler, HTTPErrorProcessor, DataHandler, \
    HTTPSHandler, HTTPCookieProcessor


class Response:
    def __init__(self, response):
        self._response = response
        self.status = response.status
        self.reason = response.reason
        self.headers = dict(response.info())
        self._content = None
        self._text = None

    @property
    def text(self):
        if self._text is None:
            self._text = self._response.read().decode('utf-8')
        return self._text

    @property
    def content(self):
        if self._content is None:
            self._content = self._response.read()
        return self._content

    def json(self):
        return json.loads(self.text)


class request(OpenerDirector):
    def __init__(self):
        super().__init__()
        cj = CookieJar()
        handlers = [HTTPSHandler(context=ssl.SSLContext(ssl.PROTOCOL_SSLv23)), HTTPCookieProcessor(cj)]

        default_classes = [UnknownHandler, HTTPHandler,
                           HTTPDefaultErrorHandler, HTTPRedirectHandler,
                           FTPHandler, FileHandler, HTTPErrorProcessor,
                           DataHandler]
        if hasattr(http.client, "HTTPSConnection"):
            default_classes.append(HTTPSHandler)
        skip = set()
        for klass in default_classes:
            for check in handlers:
                if isinstance(check, type):
                    if issubclass(check, klass):
                        skip.add(klass)
                elif isinstance(check, klass):
                    skip.add(klass)
        for klass in skip:
            default_classes.remove(klass)

        for klass in default_classes:
            self.add_handler(klass())

        for h in handlers:
            if isinstance(h, type):
                h = h()
            self.add_handler(h)

        # Agrega un User-Agent por defecto
        self.add_headers({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3'
        })

    def open(self, fullurl, data=None, timeout=socket._GLOBAL_DEFAULT_TIMEOUT, headers=None) -> Response:
        try:
            if isinstance(data, dict):
                data = urlencode(data).encode('ascii')
            elif isinstance(data, str):
                data = data.encode('ascii')
            if headers:
                self.add_headers(headers)

            response = super().open(fullurl, data, timeout=timeout)
            return Response(response)
        except HTTPError as e:
            raise URLError(f"Error HTTP {e.code}: {e.reason}")
        except Exception as e:
            raise URLError(f"Error al abrir URL: {fullurl} Detalle: {str(e)}")

    def addProxy(self, host="", username="", password="", typos=None):
        if host:
            if typos is None:
                typos = ("https", "http")
            elif isinstance(typos, str):
                typos = (typos,)
            for typo in typos:
                if username and password:
                    proxies = {typo: f'{typo}://{username}:{password}@{host}'}
                else:
                    proxies = {typo: f'{typo}://{host}'}
                handlers = ProxyHandler(proxies)
                self.add_handler(handlers)

    def add_headers(self, headers) -> None:
        if isinstance(headers, dict):
            self.addheaders.extend(headers.items())
        elif isinstance(headers, tuple):
            self.addheaders.append(headers)
        elif isinstance(headers, list):
            self.addheaders.extend(headers)

    def get(self, url, params=None, **kwargs):
        if params:
            url = f"{url}?{urlencode(params)}"
        return self.open(url, **kwargs)

    def post(self, url, data=None, _json=None, **kwargs):
        if _json is not None:
            data = json.dumps(_json)
            kwargs.setdefault('headers', {}).update({
                'Content-Type': 'application/json'
            })
        return self.open(url, data, **kwargs)

    def put(self, url, data=None, **kwargs):
        return self.open(url, data, method='PUT', **kwargs)

    def delete(self, url, **kwargs):
        return self.open(url, method='DELETE', **kwargs)

    def head(self, url, **kwargs):
        return self.open(url, method='HEAD', **kwargs)

    def options(self, url, **kwargs):
        return self.open(url, method='OPTIONS', **kwargs)

    def patch(self, url, data=None, **kwargs):
        return self.open(url, data, method='PATCH', **kwargs)