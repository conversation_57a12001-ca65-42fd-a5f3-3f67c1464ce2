from datetime import datetime
from typing import Tuple

import uuid
from json import dumps

from Cryptodome.Util.py3compat import tobytes
from OpenSSL.crypto import load_certificate, FILETYPE_PEM, TYPE_RSA, PKey, X509, dump_privatekey, dump_certificate, \
    load_privatekey, dump_publickey, load_publickey


def crearCert(file, passw, properties: dict, para: str, *, IDCliente:str=None, IDPlugin: str=None,
              EmitidoPor: str='Cert. One For All', before: int or None = -86400,
              after: int or None = 31104000, serial_number: int=None):
    private_key = load_privatekey(FILETYPE_PEM, open(file, 'rb').read(), tobytes(passw) if passw else None)
    public_key = load_publickey(FILETYPE_PEM, open(file, 'rb').read())

    cert = X509()
    cert.set_version(2)
    cert.get_subject().CN = para  # Nombre de objetivo del certificado
    if properties is not None:
        cert.get_subject().DC = dumps(properties)  # Propiedades del certificado

    if IDCliente is not None:
        cert.get_subject().ST = IDCliente  # Para cliente OFA especifica
    if IDPlugin is not None:
        cert.get_subject().O = IDPlugin
    # cert.get_subject().ST = "Jerusalem"
    # cert.get_subject().L = "Jerusalem"
    # cert.get_subject().OU = "DevOps Loft"

    cert.get_issuer().CN = EmitidoPor  # Emisor del certificado
    if isinstance(before, int):
        cert.gmtime_adj_notBefore(before)
        cert.gmtime_adj_notAfter(after)
    cert.set_serial_number(serial_number or uuid.uuid4().int)
    cert.set_pubkey(public_key)
    cert.sign(private_key, "sha256")
    return dump_certificate(FILETYPE_PEM, cert)


def crearPrivateKey(passw: str, bits=2048) -> Tuple[bytes, bytes]:
    key = PKey()
    key.generate_key(TYPE_RSA, bits)
    return dump_privatekey(FILETYPE_PEM, key, passphrase=tobytes(passw)), dump_publickey(FILETYPE_PEM, key)


def loadCert(file):
    if isinstance(file, str):
        return load_certificate(FILETYPE_PEM, open(file, 'rb').read())
    else:
        return load_certificate(FILETYPE_PEM, file)


def isValid(data, public_key):
    crt = loadCert(data)
    if dump_publickey(FILETYPE_PEM, crt.get_pubkey()) == public_key:
        if not datetime.strptime(crt.get_notBefore().decode("utf-8"), "%Y%m%d%H%M%SZ") < datetime.utcnow() < datetime.strptime(crt.get_notAfter().decode("utf-8"), "%Y%m%d%H%M%SZ"):
            return
        return crt

if __name__ == '__main__':
    # with open("_data/PribateKeyFromFlask.key", "wb") as f:
    #     a=crearPrivateKey("OneForAll_Password", 5120)
    #     print(a)
    #     for i in a:
    #         f.write(i)

    # with open("_data/CertificateFromFlask.crt", "wb") as f:
    #     a = crearCert("_data/PribateKeyFromFlask.key", "", {}, "OneForAll_Server_WEB")
    #     print(a)
    #     f.write(a)

    pass