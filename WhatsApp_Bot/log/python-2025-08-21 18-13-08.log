🚀 Iniciando bot de WhatsApp...
2025-08-21 18:13:08,213 whatsapp_bot - add_message_handler INFO -> Handler a�adido: message_handler
2025-08-21 18:13:08,214 whatsapp_bot - _initialize_session INFO -> Inicializando sesi�n de WhatsApp...
2025-08-21 18:13:10,221 whatsapp_bot - _load_contacts INFO -> Cargados 3 contactos
2025-08-21 18:13:10,236 whatsapp_bot - start_bot INFO -> Bo<PERSON> de WhatsApp iniciado correctamente
✅ Bot iniciado correctamente
2025-08-21 18:13:10,236 OFA_Pipe - SendCommand WARNING -> RETURN: El socket no esta conectado para enviar: add_contact
2025-08-21 18:13:10,236 OFA_Pipe - SendCommand WARNING -> RETURN: El socket no esta conectado para enviar: add_contact
2025-08-21 18:13:10,236 OFA_Pipe - SendCommand WARNING -> REGISTER: El socket no esta conectado para enviar: start_monitoring
👀 Monitoreo de mensajes iniciado
2025-08-21 18:13:10,238 OFA_Pipe - _monitor_events ERROR -> Error en monitor de eventos: Proactor event loop does not implement add_reader family of methods required for zmq. zmq will work with proactor if tornado >= 6.1 can be found. Use `asyncio.set_event_loop_policy(WindowsSelectorEventLoopPolicy())` or install 'tornado>=6.1' to avoid this error.
2025-08-21 18:13:10,345 asyncio - default_exception_handler ERROR -> Task exception was never retrieved
future: <Task finished name='Task-2' coro=<OFA_Pipe._monitor_events() done, defined at D:\_Proyectos\_Python\_libs\OFA_Pipe\__init__.py:330> exception=RuntimeError("Proactor event loop does not implement add_reader family of methods required for zmq. zmq will work with proactor if tornado >= 6.1 can be found. Use `asyncio.set_event_loop_policy(WindowsSelectorEventLoopPolicy())` or install 'tornado>=6.1' to avoid this error.")>
Traceback (most recent call last):
  File "D:\_Proyectos\_Python\OFA-GUI\.venv\Lib\site-packages\zmq\asyncio.py", line 51, in _get_selector_windows
    from tornado.platform.asyncio import AddThreadSelectorEventLoop
ModuleNotFoundError: No module named 'tornado'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\_Proyectos\_Python\_libs\OFA_Pipe\__init__.py", line 381, in _monitor_events
    monitor_socket.close()
    ~~~~~~~~~~~~~~~~~~~~^^
  File "D:\_Proyectos\_Python\OFA-GUI\.venv\Lib\site-packages\zmq\_future.py", line 257, in close
    self._clear_io_state()
    ~~~~~~~~~~~~~~~~~~~~^^
  File "D:\_Proyectos\_Python\OFA-GUI\.venv\Lib\site-packages\zmq\asyncio.py", line 161, in _clear_io_state
    self._get_selector(loop).remove_reader(self._fd)
    ~~~~~~~~~~~~~~~~~~^^^^^^
  File "D:\_Proyectos\_Python\OFA-GUI\.venv\Lib\site-packages\zmq\asyncio.py", line 146, in _get_selector
    return _get_selector(io_loop)
  File "D:\_Proyectos\_Python\OFA-GUI\.venv\Lib\site-packages\zmq\asyncio.py", line 53, in _get_selector_windows
    raise RuntimeError(
    ...<4 lines>...
    )
RuntimeError: Proactor event loop does not implement add_reader family of methods required for zmq. zmq will work with proactor if tornado >= 6.1 can be found. Use `asyncio.set_event_loop_policy(WindowsSelectorEventLoopPolicy())` or install 'tornado>=6.1' to avoid this error.
2025-08-21 18:13:12,246 OFA_Pipe - SendCommand WARNING -> RETURN: El socket no esta conectado para enviar: get_contacts
📞 Contactos cargados: Desconectado
2025-08-21 18:13:12,246 OFA_Pipe - SendCommand WARNING -> RETURN: El socket no esta conectado para enviar: get_chats
Traceback (most recent call last):
  File "D:\_Proyectos\_Python\One_For_All_Plugin\WhatsApp_Bot\example_usage.py", line 77, in main
    print(f"💬 Chats encontrados: {len(chats)}")
                                   ~~~^^^^^^^
TypeError: object of type 'NoneType' has no len()
2025-08-21 18:13:12,248 __main__ - main ERROR -> Error en main: object of type 'NoneType' has no len()
Exception ignored in: <function Socket.__del__ at 0x000001F9B7D1C9A0>
Traceback (most recent call last):
  File "D:\_Proyectos\_Python\OFA-GUI\.venv\Lib\site-packages\zmq\sugar\socket.py", line 194, in __del__
    self.close()
  File "D:\_Proyectos\_Python\OFA-GUI\.venv\Lib\site-packages\zmq\_future.py", line 257, in close
    self._clear_io_state()
  File "D:\_Proyectos\_Python\OFA-GUI\.venv\Lib\site-packages\zmq\asyncio.py", line 161, in _clear_io_state
    self._get_selector(loop).remove_reader(self._fd)
  File "D:\_Proyectos\_Python\OFA-GUI\.venv\Lib\site-packages\zmq\asyncio.py", line 146, in _get_selector
    return _get_selector(io_loop)
  File "D:\_Proyectos\_Python\OFA-GUI\.venv\Lib\site-packages\zmq\asyncio.py", line 53, in _get_selector_windows
    raise RuntimeError(
RuntimeError: Proactor event loop does not implement add_reader family of methods required for zmq. zmq will work with proactor if tornado >= 6.1 can be found. Use `asyncio.set_event_loop_policy(WindowsSelectorEventLoopPolicy())` or install 'tornado>=6.1' to avoid this error.
2025-08-21 18:13:12,252 OFA_Pipe - close WARNING -> WhatsAppBot cerrado correctamente
2025-08-21 18:13:12,253 whatsapp_bot - stop INFO -> Bot detenido
