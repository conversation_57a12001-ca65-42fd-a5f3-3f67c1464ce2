🚀 Iniciando bot de WhatsApp...
2025-08-21 18:14:01,211 whatsapp_bot - add_message_handler INFO -> Handler a�adido: message_handler
2025-08-21 18:14:01,211 whatsapp_bot - _initialize_session INFO -> Inicializando sesi�n de WhatsApp...
2025-08-21 18:14:03,220 whatsapp_bot - _load_contacts INFO -> Cargados 3 contactos
2025-08-21 18:14:03,234 whatsapp_bot - start_bot INFO -> Bo<PERSON> de WhatsApp iniciado correctamente
✅ Bot iniciado correctamente
2025-08-21 18:14:03,235 OFA_Pipe - SendCommand WARNING -> RETURN: El socket no esta conectado para enviar: add_contact
2025-08-21 18:14:03,235 OFA_Pipe - SendCommand WARNING -> RETURN: El socket no esta conectado para enviar: add_contact
2025-08-21 18:14:03,235 OFA_Pipe - SendCommand WARNING -> REGISTER: El socket no esta conectado para enviar: start_monitoring
👀 Monitoreo de mensajes iniciado
D:\_Proyectos\_Python\OFA-CORE\.venv\Lib\site-packages\zmq\_future.py:718: RuntimeWarning: Proactor event loop does not implement add_reader family of methods required for zmq. Registering an additional selector thread for add_reader support via tornado. Use `asyncio.set_event_loop_policy(WindowsSelectorEventLoopPolicy())` to avoid this warning.
  self._get_loop()
2025-08-21 18:14:05,251 OFA_Pipe - SendCommand WARNING -> RETURN: El socket no esta conectado para enviar: get_contacts
📞 Contactos cargados: Desconectado
2025-08-21 18:14:05,251 OFA_Pipe - SendCommand WARNING -> RETURN: El socket no esta conectado para enviar: get_chats
Traceback (most recent call last):
  File "D:\_Proyectos\_Python\One_For_All_Plugin\WhatsApp_Bot\example_usage.py", line 77, in main
    print(f"💬 Chats encontrados: {len(chats)}")
                                   ~~~^^^^^^^
TypeError: object of type 'NoneType' has no len()
2025-08-21 18:14:05,253 __main__ - main ERROR -> Error en main: object of type 'NoneType' has no len()
2025-08-21 18:14:05,254 OFA_Pipe - close WARNING -> WhatsAppBot cerrado correctamente
2025-08-21 18:14:05,254 whatsapp_bot - stop INFO -> Bot detenido
👋 Bot detenido
