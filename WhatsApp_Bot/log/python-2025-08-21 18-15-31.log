🚀 Iniciando bot de WhatsApp...
2025-08-21 18:15:31,503 whatsapp_bot - add_message_handler INFO -> Handler a�adido: message_handler
2025-08-21 18:15:31,503 whatsapp_bot - _initialize_session INFO -> Inicializando sesi�n de WhatsApp...
2025-08-21 18:15:33,508 whatsapp_bot - _load_contacts INFO -> Cargados 3 contactos
2025-08-21 18:15:33,523 whatsapp_bot - start_bot INFO -> Bo<PERSON> de WhatsApp iniciado correctamente
✅ Bot iniciado correctamente
2025-08-21 18:15:33,523 OFA_Pipe - SendCommand WARNING -> RETURN: El socket no esta conectado para enviar: add_contact
2025-08-21 18:15:33,524 OFA_Pipe - SendCommand WARNING -> RETURN: El socket no esta conectado para enviar: add_contact
2025-08-21 18:15:33,524 OFA_Pipe - SendCommand WARNING -> REGISTER: El socket no esta conectado para enviar: start_monitoring
👀 Monitoreo de mensajes iniciado
D:\_Proyectos\_Python\OFA-CORE\.venv\Lib\site-packages\zmq\_future.py:718: RuntimeWarning: Proactor event loop does not implement add_reader family of methods required for zmq. Registering an additional selector thread for add_reader support via tornado. Use `asyncio.set_event_loop_policy(WindowsSelectorEventLoopPolicy())` to avoid this warning.
  self._get_loop()
2025-08-21 18:15:35,528 OFA_Pipe - SendCommand WARNING -> RETURN: El socket no esta conectado para enviar: get_contacts
📞 Contactos cargados: Desconectado
2025-08-21 18:15:35,529 OFA_Pipe - SendCommand WARNING -> RETURN: El socket no esta conectado para enviar: get_chats
💬 Chats encontrados: Desconectado
2025-08-21 18:15:35,529 OFA_Pipe - SendCommand WARNING -> RETURN: El socket no esta conectado para enviar: send_message
2025-08-21 18:15:35,529 OFA_Pipe - SendCommand WARNING -> RETURN: El socket no esta conectado para enviar: get_status
📊 Estado: None

🔄 Bot corriendo... Presiona Ctrl+C para detener
💡 Los mensajes simulados aparecerán cada 30 segundos
2025-08-21 18:15:46,980 OFA_Pipe - close WARNING -> WhatsAppBot cerrado correctamente
2025-08-21 18:15:46,980 whatsapp_bot - stop INFO -> Bot detenido
👋 Bot detenido
Traceback (most recent call last):
  File "D:\_Proyectos\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
  File "D:\_Proyectos\Python313\Lib\asyncio\base_events.py", line 725, in run_until_complete
    return future.result()
           ~~~~~~~~~~~~~^^
  File "D:\_Proyectos\_Python\One_For_All_Plugin\WhatsApp_Bot\example_usage.py", line 91, in main
    await asyncio.sleep(1)
  File "D:\_Proyectos\Python313\Lib\asyncio\tasks.py", line 718, in sleep
    return await future
           ^^^^^^^^^^^^
asyncio.exceptions.CancelledError

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\_Proyectos\_Python\One_For_All_Plugin\WhatsApp_Bot\example_usage.py", line 104, in <module>
    asyncio.run(main())
    ~~~~~~~~~~~^^^^^^^^
  File "D:\_Proyectos\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           ~~~~~~~~~~^^^^^^
  File "D:\_Proyectos\Python313\Lib\asyncio\runners.py", line 123, in run
    raise KeyboardInterrupt()
KeyboardInterrupt
