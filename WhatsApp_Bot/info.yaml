name: "WhatsApp Bot"
version: "1.0.0"
description: "Bot de WhatsApp integrado con funcionalidades avanzadas"
author: "OneForAll Plugin"
category: "Communication"
icon: "Icono.png"
main_class: "WhatsAppBot"
main_file: "core/bot.py"
gui_file: "gui/main_window.py"
dependencies:
  - "websockets"
  - "aiohttp"
  - "qrcode"
  - "pillow"
  - "requests"
  - "python-dateutil"
  - "asyncio"
features:
  - "Conexión automática a WhatsApp Web"
  - "Sistema de comandos extensible"
  - "Respuestas automáticas"
  - "Gestión de grupos"
  - "Interfaz gráfica"
  - "Sistema de logs"
  - "Configuración flexible"
commands:
  - name: "ping"
    description: "Verifica la conexión del bot"
    usage: "!ping"
  - name: "info"
    description: "Muestra información del bot"
    usage: "!info"
  - name: "time"
    description: "Muestra la hora actual"
    usage: "!time"
  - name: "help"
    description: "Muestra la lista de comandos"
    usage: "!help"
requirements:
  python: ">=3.8"
  chrome: "Navegador Chrome instalado"
  chromedriver: "Se instala automáticamente"
