"""
Funcionalidades avanzadas del bot de WhatsApp
"""
import json
import os
import time
import re
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
from .logger import bot_logger

class AutoResponder:
    """Sistema de respuestas automáticas avanzadas"""
    
    def __init__(self, config):
        self.config = config
        self.response_patterns = {}
        self.response_stats = {}
        self.load_patterns()
    
    def load_patterns(self):
        """Carga patrones de respuesta desde archivo"""
        patterns_file = os.path.join(os.path.dirname(__file__), "..", "patterns.json")
        try:
            if os.path.exists(patterns_file):
                with open(patterns_file, 'r', encoding='utf-8') as f:
                    self.response_patterns = json.load(f)
                bot_logger.info(f"Cargados {len(self.response_patterns)} patrones de respuesta")
        except Exception as e:
            bot_logger.error(f"Error cargando patrones: {e}")
    
    def save_patterns(self):
        """Guarda patrones de respuesta a archivo"""
        patterns_file = os.path.join(os.path.dirname(__file__), "..", "patterns.json")
        try:
            with open(patterns_file, 'w', encoding='utf-8') as f:
                json.dump(self.response_patterns, f, ensure_ascii=False, indent=2)
            bot_logger.info("Patrones guardados exitosamente")
        except Exception as e:
            bot_logger.error(f"Error guardando patrones: {e}")
    
    def add_pattern(self, pattern: str, responses: List[str], regex: bool = False):
        """Añade un patrón de respuesta"""
        self.response_patterns[pattern] = {
            "responses": responses,
            "regex": regex,
            "created": datetime.now().isoformat(),
            "usage_count": 0
        }
        self.save_patterns()
    
    def remove_pattern(self, pattern: str):
        """Remueve un patrón de respuesta"""
        if pattern in self.response_patterns:
            del self.response_patterns[pattern]
            self.save_patterns()
    
    def get_response(self, message: str) -> Optional[str]:
        """Obtiene respuesta para un mensaje"""
        message_lower = message.lower().strip()
        
        for pattern, data in self.response_patterns.items():
            match = False
            
            if data.get("regex", False):
                # Usar expresión regular
                try:
                    match = bool(re.search(pattern, message_lower, re.IGNORECASE))
                except re.error:
                    continue
            else:
                # Búsqueda simple
                match = pattern.lower() in message_lower
            
            if match:
                # Incrementar contador de uso
                data["usage_count"] = data.get("usage_count", 0) + 1
                
                # Seleccionar respuesta aleatoria
                import random
                response = random.choice(data["responses"])
                
                # Reemplazar variables
                response = self._replace_variables(response)
                
                return response
        
        return None
    
    def _replace_variables(self, response: str) -> str:
        """Reemplaza variables en la respuesta"""
        now = datetime.now()
        
        replacements = {
            "{time}": now.strftime("%H:%M"),
            "{date}": now.strftime("%d/%m/%Y"),
            "{day}": now.strftime("%A"),
            "{bot_name}": self.config.bot_name,
            "{version}": self.config.version
        }
        
        for var, value in replacements.items():
            response = response.replace(var, value)
        
        return response

class GroupManager:
    """Gestor de grupos y permisos"""
    
    def __init__(self, config):
        self.config = config
        self.group_settings = {}
        self.user_permissions = {}
        self.load_settings()
    
    def load_settings(self):
        """Carga configuraciones de grupos"""
        settings_file = os.path.join(os.path.dirname(__file__), "..", "group_settings.json")
        try:
            if os.path.exists(settings_file):
                with open(settings_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.group_settings = data.get("groups", {})
                    self.user_permissions = data.get("users", {})
                bot_logger.info("Configuraciones de grupo cargadas")
        except Exception as e:
            bot_logger.error(f"Error cargando configuraciones: {e}")
    
    def save_settings(self):
        """Guarda configuraciones de grupos"""
        settings_file = os.path.join(os.path.dirname(__file__), "..", "group_settings.json")
        try:
            data = {
                "groups": self.group_settings,
                "users": self.user_permissions,
                "updated": datetime.now().isoformat()
            }
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            bot_logger.info("Configuraciones guardadas")
        except Exception as e:
            bot_logger.error(f"Error guardando configuraciones: {e}")
    
    def set_group_setting(self, group_id: str, setting: str, value: Any):
        """Configura una opción de grupo"""
        if group_id not in self.group_settings:
            self.group_settings[group_id] = {}
        
        self.group_settings[group_id][setting] = value
        self.save_settings()
    
    def get_group_setting(self, group_id: str, setting: str, default: Any = None):
        """Obtiene una configuración de grupo"""
        return self.group_settings.get(group_id, {}).get(setting, default)
    
    def is_group_allowed(self, group_id: str) -> bool:
        """Verifica si el bot puede funcionar en un grupo"""
        if not self.config.allowed_groups:
            return True  # Si no hay restricciones, permitir todos
        
        return group_id in self.config.allowed_groups
    
    def can_use_commands(self, group_id: str, user_id: str) -> bool:
        """Verifica si un usuario puede usar comandos en un grupo"""
        if not self.config.group_commands_enabled:
            return False
        
        # Verificar si el grupo permite comandos
        if not self.get_group_setting(group_id, "commands_enabled", True):
            return False
        
        # Verificar permisos del usuario
        user_level = self.get_user_permission(user_id)
        min_level = self.get_group_setting(group_id, "min_command_level", 0)
        
        return user_level >= min_level
    
    def set_user_permission(self, user_id: str, level: int):
        """Establece nivel de permisos de usuario"""
        self.user_permissions[user_id] = {
            "level": level,
            "updated": datetime.now().isoformat()
        }
        self.save_settings()
    
    def get_user_permission(self, user_id: str) -> int:
        """Obtiene nivel de permisos de usuario"""
        if self.config.is_admin(user_id):
            return 100  # Nivel máximo para admins
        
        return self.user_permissions.get(user_id, {}).get("level", 0)

class MessageScheduler:
    """Programador de mensajes"""
    
    def __init__(self):
        self.scheduled_messages = []
        self.is_running = False
    
    def schedule_message(self, chat_id: str, message: str, send_time: datetime, repeat: str = None):
        """Programa un mensaje"""
        scheduled_msg = {
            "id": len(self.scheduled_messages),
            "chat_id": chat_id,
            "message": message,
            "send_time": send_time,
            "repeat": repeat,  # "daily", "weekly", "monthly", None
            "created": datetime.now(),
            "sent": False
        }
        
        self.scheduled_messages.append(scheduled_msg)
        bot_logger.info(f"Mensaje programado para {send_time}")
        
        return scheduled_msg["id"]
    
    def cancel_message(self, message_id: int):
        """Cancela un mensaje programado"""
        for msg in self.scheduled_messages:
            if msg["id"] == message_id and not msg["sent"]:
                msg["cancelled"] = True
                return True
        return False
    
    def get_pending_messages(self) -> List[Dict]:
        """Obtiene mensajes pendientes"""
        now = datetime.now()
        pending = []
        
        for msg in self.scheduled_messages:
            if (not msg.get("sent", False) and 
                not msg.get("cancelled", False) and 
                msg["send_time"] <= now):
                pending.append(msg)
        
        return pending
    
    def mark_as_sent(self, message_id: int):
        """Marca un mensaje como enviado"""
        for msg in self.scheduled_messages:
            if msg["id"] == message_id:
                msg["sent"] = True
                msg["sent_time"] = datetime.now()
                
                # Programar siguiente envío si es repetitivo
                if msg.get("repeat"):
                    self._schedule_repeat(msg)
                break
    
    def _schedule_repeat(self, msg: Dict):
        """Programa repetición de mensaje"""
        repeat_type = msg["repeat"]
        next_time = msg["send_time"]
        
        if repeat_type == "daily":
            next_time += timedelta(days=1)
        elif repeat_type == "weekly":
            next_time += timedelta(weeks=1)
        elif repeat_type == "monthly":
            next_time += timedelta(days=30)  # Aproximado
        
        if next_time:
            self.schedule_message(
                msg["chat_id"],
                msg["message"],
                next_time,
                repeat_type
            )

class StatisticsCollector:
    """Recolector de estadísticas del bot"""
    
    def __init__(self):
        self.stats = {
            "messages_received": 0,
            "messages_sent": 0,
            "commands_executed": 0,
            "errors": 0,
            "start_time": datetime.now(),
            "daily_stats": {},
            "command_usage": {},
            "user_activity": {}
        }
    
    def record_message_received(self, sender: str, is_group: bool = False):
        """Registra mensaje recibido"""
        self.stats["messages_received"] += 1
        
        # Estadísticas diarias
        today = datetime.now().strftime("%Y-%m-%d")
        if today not in self.stats["daily_stats"]:
            self.stats["daily_stats"][today] = {"received": 0, "sent": 0, "commands": 0}
        
        self.stats["daily_stats"][today]["received"] += 1
        
        # Actividad de usuario
        if sender not in self.stats["user_activity"]:
            self.stats["user_activity"][sender] = {"messages": 0, "commands": 0, "last_seen": None}
        
        self.stats["user_activity"][sender]["messages"] += 1
        self.stats["user_activity"][sender]["last_seen"] = datetime.now().isoformat()
    
    def record_message_sent(self):
        """Registra mensaje enviado"""
        self.stats["messages_sent"] += 1
        
        today = datetime.now().strftime("%Y-%m-%d")
        if today in self.stats["daily_stats"]:
            self.stats["daily_stats"][today]["sent"] += 1
    
    def record_command_executed(self, command: str, sender: str):
        """Registra comando ejecutado"""
        self.stats["commands_executed"] += 1
        
        # Uso de comandos
        if command not in self.stats["command_usage"]:
            self.stats["command_usage"][command] = 0
        self.stats["command_usage"][command] += 1
        
        # Estadísticas diarias
        today = datetime.now().strftime("%Y-%m-%d")
        if today in self.stats["daily_stats"]:
            self.stats["daily_stats"][today]["commands"] += 1
        
        # Actividad de usuario
        if sender in self.stats["user_activity"]:
            self.stats["user_activity"][sender]["commands"] += 1
    
    def record_error(self):
        """Registra error"""
        self.stats["errors"] += 1
    
    def get_summary(self) -> Dict:
        """Obtiene resumen de estadísticas"""
        uptime = datetime.now() - self.stats["start_time"]
        
        return {
            "uptime_hours": uptime.total_seconds() / 3600,
            "messages_received": self.stats["messages_received"],
            "messages_sent": self.stats["messages_sent"],
            "commands_executed": self.stats["commands_executed"],
            "errors": self.stats["errors"],
            "most_used_commands": sorted(
                self.stats["command_usage"].items(),
                key=lambda x: x[1],
                reverse=True
            )[:5],
            "active_users": len(self.stats["user_activity"])
        }
    
    def save_stats(self):
        """Guarda estadísticas a archivo"""
        stats_file = os.path.join(os.path.dirname(__file__), "..", "stats.json")
        try:
            # Convertir datetime a string para JSON
            stats_copy = self.stats.copy()
            stats_copy["start_time"] = stats_copy["start_time"].isoformat()
            
            with open(stats_file, 'w', encoding='utf-8') as f:
                json.dump(stats_copy, f, ensure_ascii=False, indent=2)
        except Exception as e:
            bot_logger.error(f"Error guardando estadísticas: {e}")

# Instancias globales
auto_responder = None
group_manager = None
message_scheduler = MessageScheduler()
stats_collector = StatisticsCollector()
