"""
Sistema de comandos para el bot de WhatsApp
"""
import time
from datetime import datetime
from typing import Dict, Callable, Any, Optional
from .logger import bot_logger

class Command:
    """Clase para representar un comando"""
    
    def __init__(self, name: str, description: str, handler: Callable, admin_only: bool = False):
        self.name = name
        self.description = description
        self.handler = handler
        self.admin_only = admin_only

class CommandManager:
    """Manejador de comandos del bot"""
    
    def __init__(self, config):
        self.config = config
        self.commands: Dict[str, Command] = {}
        self._register_default_commands()
    
    def register_command(self, name: str, description: str, handler: Callable, admin_only: bool = False):
        """Registra un nuevo comando"""
        command = Command(name, description, handler, admin_only)
        self.commands[name.lower()] = command
        bot_logger.info(f"Comando registrado: {name}")
    
    def unregister_command(self, name: str):
        """Desregistra un comando"""
        name_lower = name.lower()
        if name_lower in self.commands:
            del self.commands[name_lower]
            bot_logger.info(f"Comando desregistrado: {name}")
    
    def execute_command(self, command_text: str, sender: str, chat_id: str, is_group: bool = False) -> Optional[str]:
        """Ejecuta un comando"""
        if not command_text.startswith(self.config.command_prefix):
            return None
        
        # Extraer comando y argumentos
        parts = command_text[1:].split()
        if not parts:
            return None
        
        command_name = parts[0].lower()
        args = parts[1:] if len(parts) > 1 else []
        
        # Verificar si el comando existe
        if command_name not in self.commands:
            return f"Comando '{command_name}' no encontrado. Usa {self.config.command_prefix}help para ver comandos disponibles."
        
        command = self.commands[command_name]
        
        # Verificar permisos de administrador
        if command.admin_only and not self.config.is_admin(sender):
            bot_logger.log_command(sender, command_text, False)
            return "❌ Este comando requiere permisos de administrador."
        
        # Verificar si los comandos están habilitados en grupos
        if is_group and not self.config.group_commands_enabled:
            return "❌ Los comandos están deshabilitados en grupos."
        
        try:
            # Ejecutar comando
            result = command.handler(args, sender, chat_id, is_group)
            bot_logger.log_command(sender, command_text, True)
            return result
        except Exception as e:
            bot_logger.error(f"Error ejecutando comando '{command_name}': {str(e)}")
            bot_logger.log_command(sender, command_text, False)
            return f"❌ Error ejecutando comando: {str(e)}"
    
    def get_help_text(self, sender: str) -> str:
        """Genera texto de ayuda con comandos disponibles"""
        help_lines = [f"🤖 *{self.config.bot_name} - Comandos Disponibles*\n"]
        
        for name, command in self.commands.items():
            # Mostrar solo comandos que el usuario puede usar
            if command.admin_only and not self.config.is_admin(sender):
                continue
            
            help_lines.append(f"• {self.config.command_prefix}{name} - {command.description}")
        
        return "\n".join(help_lines)
    
    def _register_default_commands(self):
        """Registra comandos por defecto"""
        
        def cmd_ping(args, sender, chat_id, is_group):
            """Comando ping"""
            return "🏓 Pong! El bot está funcionando correctamente."
        
        def cmd_info(args, sender, chat_id, is_group):
            """Comando info"""
            return f"🤖 *{self.config.bot_name}*\nVersión: {self.config.version}\nDesarrollado por OneForAll Plugin"
        
        def cmd_time(args, sender, chat_id, is_group):
            """Comando time"""
            now = datetime.now()
            return f"🕐 Hora actual: {now.strftime('%H:%M:%S')}\n📅 Fecha: {now.strftime('%d/%m/%Y')}"
        
        def cmd_help(args, sender, chat_id, is_group):
            """Comando help"""
            return self.get_help_text(sender)
        
        def cmd_status(args, sender, chat_id, is_group):
            """Comando status (solo admin)"""
            uptime = time.time() - getattr(self.config, 'start_time', time.time())
            hours = int(uptime // 3600)
            minutes = int((uptime % 3600) // 60)
            return f"📊 *Estado del Bot*\n⏱️ Tiempo activo: {hours}h {minutes}m\n✅ Estado: Conectado"
        
        def cmd_admin_add(args, sender, chat_id, is_group):
            """Comando para añadir admin (solo admin)"""
            if not args:
                return "❌ Uso: !admin_add <número>"
            
            phone = args[0]
            self.config.add_admin(phone)
            return f"✅ {phone} añadido como administrador."
        
        def cmd_admin_remove(args, sender, chat_id, is_group):
            """Comando para remover admin (solo admin)"""
            if not args:
                return "❌ Uso: !admin_remove <número>"
            
            phone = args[0]
            self.config.remove_admin(phone)
            return f"✅ {phone} removido de administradores."
        
        # Registrar comandos
        self.register_command("ping", "Verifica la conexión del bot", cmd_ping)
        self.register_command("info", "Muestra información del bot", cmd_info)
        self.register_command("time", "Muestra la hora actual", cmd_time)
        self.register_command("help", "Muestra esta ayuda", cmd_help)
        self.register_command("status", "Muestra el estado del bot", cmd_status, admin_only=True)
        self.register_command("admin_add", "Añade un administrador", cmd_admin_add, admin_only=True)
        self.register_command("admin_remove", "Remueve un administrador", cmd_admin_remove, admin_only=True)
