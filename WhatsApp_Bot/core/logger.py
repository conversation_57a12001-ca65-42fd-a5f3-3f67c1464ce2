"""
Sistema de logging para el bot de WhatsApp
"""
import logging
import os
from datetime import datetime
from typing import Optional

class BotLogger:
    """Manejador de logs del bot"""
    
    def __init__(self, log_file: str = None, log_level: str = "INFO"):
        self.log_file = log_file or os.path.join(os.path.dirname(__file__), "..", "logs", "bot.log")
        self.log_level = getattr(logging, log_level.upper(), logging.INFO)
        self.logger = None
        self._setup_logger()
    
    def _setup_logger(self):
        """Configura el sistema de logging"""
        # Crear directorio de logs si no existe
        os.makedirs(os.path.dirname(self.log_file), exist_ok=True)
        
        # Configurar logger
        self.logger = logging.getLogger("WhatsAppBot")
        self.logger.setLevel(self.log_level)
        
        # Evitar duplicar handlers
        if self.logger.handlers:
            self.logger.handlers.clear()
        
        # Formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # Handler para archivo
        file_handler = logging.FileHandler(self.log_file, encoding='utf-8')
        file_handler.setLevel(self.log_level)
        file_handler.setFormatter(formatter)
        self.logger.addHandler(file_handler)
        
        # Handler para consola
        console_handler = logging.StreamHandler()
        console_handler.setLevel(self.log_level)
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)
    
    def info(self, message: str):
        """Log de información"""
        self.logger.info(message)
    
    def warning(self, message: str):
        """Log de advertencia"""
        self.logger.warning(message)
    
    def error(self, message: str):
        """Log de error"""
        self.logger.error(message)
    
    def debug(self, message: str):
        """Log de debug"""
        self.logger.debug(message)
    
    def critical(self, message: str):
        """Log crítico"""
        self.logger.critical(message)
    
    def log_message(self, sender: str, message: str, chat_type: str = "private"):
        """Log específico para mensajes de WhatsApp"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.info(f"[{chat_type.upper()}] {sender}: {message}")
    
    def log_command(self, sender: str, command: str, success: bool = True):
        """Log específico para comandos ejecutados"""
        status = "SUCCESS" if success else "FAILED"
        self.info(f"[COMMAND] {sender} executed '{command}' - {status}")
    
    def log_connection(self, status: str):
        """Log específico para estado de conexión"""
        self.info(f"[CONNECTION] {status}")

# Instancia global del logger
bot_logger = BotLogger()
