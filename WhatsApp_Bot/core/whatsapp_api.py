"""
Cliente para la API oficial de WhatsApp Business
"""
import requests
import json
import time
from typing import Dict, List, Any, Optional
from datetime import datetime
from .logger import bot_logger

class WhatsAppAPI:
    """Cliente para la API oficial de WhatsApp Business"""
    
    def __init__(self, config):
        self.config = config
        self.session = requests.Session()
        self.session.headers.update({
            'Authorization': f'Bearer {config.access_token}',
            'Content-Type': 'application/json'
        })
        
    def send_text_message(self, to: str, message: str) -> bool:
        """Envía un mensaje de texto"""
        url = f"{self.config.api_base_url}/{self.config.phone_number_id}/messages"
        
        payload = {
            "messaging_product": "whatsapp",
            "to": to,
            "type": "text",
            "text": {
                "body": message
            }
        }
        
        try:
            response = self.session.post(url, json=payload)
            response.raise_for_status()
            
            result = response.json()
            message_id = result.get('messages', [{}])[0].get('id')
            
            bot_logger.info(f"Mensaje enviado exitosamente. ID: {message_id}")
            return True
            
        except requests.exceptions.RequestException as e:
            bot_logger.error(f"Error enviando mensaje: {e}")
            if hasattr(e, 'response') and e.response:
                bot_logger.error(f"Respuesta de error: {e.response.text}")
            return False
    
    def send_template_message(self, to: str, template_name: str, language: str = "es", 
                            parameters: List[str] = None) -> bool:
        """Envía un mensaje de plantilla"""
        url = f"{self.config.api_base_url}/{self.config.phone_number_id}/messages"
        
        template_data = {
            "name": template_name,
            "language": {"code": language}
        }
        
        if parameters:
            template_data["components"] = [{
                "type": "body",
                "parameters": [{"type": "text", "text": param} for param in parameters]
            }]
        
        payload = {
            "messaging_product": "whatsapp",
            "to": to,
            "type": "template",
            "template": template_data
        }
        
        try:
            response = self.session.post(url, json=payload)
            response.raise_for_status()
            
            result = response.json()
            message_id = result.get('messages', [{}])[0].get('id')
            
            bot_logger.info(f"Mensaje de plantilla enviado. ID: {message_id}")
            return True
            
        except requests.exceptions.RequestException as e:
            bot_logger.error(f"Error enviando plantilla: {e}")
            return False
    
    def send_media_message(self, to: str, media_type: str, media_url: str, 
                          caption: str = None) -> bool:
        """Envía un mensaje multimedia"""
        url = f"{self.config.api_base_url}/{self.config.phone_number_id}/messages"
        
        media_data = {"link": media_url}
        if caption and media_type in ["image", "video"]:
            media_data["caption"] = caption
        
        payload = {
            "messaging_product": "whatsapp",
            "to": to,
            "type": media_type,
            media_type: media_data
        }
        
        try:
            response = self.session.post(url, json=payload)
            response.raise_for_status()
            
            result = response.json()
            message_id = result.get('messages', [{}])[0].get('id')
            
            bot_logger.info(f"Mensaje multimedia enviado. ID: {message_id}")
            return True
            
        except requests.exceptions.RequestException as e:
            bot_logger.error(f"Error enviando multimedia: {e}")
            return False
    
    def send_interactive_message(self, to: str, message_type: str, 
                               header: str, body: str, footer: str = None,
                               buttons: List[Dict] = None, sections: List[Dict] = None) -> bool:
        """Envía un mensaje interactivo (botones o lista)"""
        url = f"{self.config.api_base_url}/{self.config.phone_number_id}/messages"
        
        interactive_data = {
            "type": message_type,
            "header": {"type": "text", "text": header} if header else None,
            "body": {"text": body},
            "footer": {"text": footer} if footer else None
        }
        
        # Remover campos None
        interactive_data = {k: v for k, v in interactive_data.items() if v is not None}
        
        if message_type == "button" and buttons:
            interactive_data["action"] = {
                "buttons": [
                    {
                        "type": "reply",
                        "reply": {
                            "id": btn.get("id", f"btn_{i}"),
                            "title": btn.get("title", f"Botón {i+1}")
                        }
                    }
                    for i, btn in enumerate(buttons[:3])  # Máximo 3 botones
                ]
            }
        elif message_type == "list" and sections:
            interactive_data["action"] = {
                "button": "Ver opciones",
                "sections": sections
            }
        
        payload = {
            "messaging_product": "whatsapp",
            "to": to,
            "type": "interactive",
            "interactive": interactive_data
        }
        
        try:
            response = self.session.post(url, json=payload)
            response.raise_for_status()
            
            result = response.json()
            message_id = result.get('messages', [{}])[0].get('id')
            
            bot_logger.info(f"Mensaje interactivo enviado. ID: {message_id}")
            return True
            
        except requests.exceptions.RequestException as e:
            bot_logger.error(f"Error enviando mensaje interactivo: {e}")
            return False
    
    def mark_message_as_read(self, message_id: str) -> bool:
        """Marca un mensaje como leído"""
        url = f"{self.config.api_base_url}/{self.config.phone_number_id}/messages"
        
        payload = {
            "messaging_product": "whatsapp",
            "status": "read",
            "message_id": message_id
        }
        
        try:
            response = self.session.post(url, json=payload)
            response.raise_for_status()
            return True
            
        except requests.exceptions.RequestException as e:
            bot_logger.error(f"Error marcando mensaje como leído: {e}")
            return False
    
    def get_media_url(self, media_id: str) -> Optional[str]:
        """Obtiene la URL de un archivo multimedia"""
        url = f"{self.config.api_base_url}/{media_id}"
        
        try:
            response = self.session.get(url)
            response.raise_for_status()
            
            result = response.json()
            return result.get('url')
            
        except requests.exceptions.RequestException as e:
            bot_logger.error(f"Error obteniendo URL de media: {e}")
            return None
    
    def download_media(self, media_url: str, filename: str) -> bool:
        """Descarga un archivo multimedia"""
        try:
            response = self.session.get(media_url)
            response.raise_for_status()
            
            with open(filename, 'wb') as f:
                f.write(response.content)
            
            bot_logger.info(f"Media descargado: {filename}")
            return True
            
        except requests.exceptions.RequestException as e:
            bot_logger.error(f"Error descargando media: {e}")
            return False
    
    def get_business_profile(self) -> Optional[Dict]:
        """Obtiene el perfil del negocio"""
        url = f"{self.config.api_base_url}/{self.config.phone_number_id}/whatsapp_business_profile"
        
        try:
            response = self.session.get(url, params={"fields": "about,address,description,email,profile_picture_url,websites,vertical"})
            response.raise_for_status()
            
            result = response.json()
            return result.get('data', [{}])[0] if result.get('data') else None
            
        except requests.exceptions.RequestException as e:
            bot_logger.error(f"Error obteniendo perfil: {e}")
            return None
    
    def update_business_profile(self, profile_data: Dict) -> bool:
        """Actualiza el perfil del negocio"""
        url = f"{self.config.api_base_url}/{self.config.phone_number_id}/whatsapp_business_profile"
        
        try:
            response = self.session.post(url, json=profile_data)
            response.raise_for_status()
            
            bot_logger.info("Perfil actualizado exitosamente")
            return True
            
        except requests.exceptions.RequestException as e:
            bot_logger.error(f"Error actualizando perfil: {e}")
            return False
    
    def validate_webhook_signature(self, payload: str, signature: str) -> bool:
        """Valida la firma del webhook"""
        import hmac
        import hashlib
        
        expected_signature = hmac.new(
            self.config.app_secret.encode('utf-8'),
            payload.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        
        return hmac.compare_digest(f"sha256={expected_signature}", signature)
    
    def test_connection(self) -> bool:
        """Prueba la conexión con la API"""
        try:
            profile = self.get_business_profile()
            if profile:
                bot_logger.info("Conexión con API de WhatsApp exitosa")
                return True
            else:
                bot_logger.error("No se pudo obtener perfil del negocio")
                return False
                
        except Exception as e:
            bot_logger.error(f"Error probando conexión: {e}")
            return False
