"""
Núcleo principal del bot de WhatsApp usando API oficial
"""
import time
import json
import os
import threading
import asyncio
from typing import Optional, Dict, Any, Callable
from datetime import datetime

from ..config import config
from .logger import bot_logger
from .commands import CommandManager
from .advanced_features import AutoResponder, GroupManager, MessageScheduler, StatisticsCollector
from .whatsapp_api import WhatsAppAPI
from .webhook_server import WebhookServer

class WhatsAppBot:
    """Bot principal de WhatsApp usando API oficial"""

    def __init__(self):
        self.config = config
        self.api_client: Optional[WhatsAppAPI] = None
        self.webhook_server: Optional[WebhookServer] = None
        self.command_manager = CommandManager(self.config)
        self.is_connected = False
        self.is_running = False
        self.message_handlers: Dict[str, Callable] = {}
        self.current_chat_id = None

        # Funcionalidades avanzadas
        self.auto_responder = AutoResponder(self.config)
        self.group_manager = GroupManager(self.config)
        self.message_scheduler = MessageScheduler()
        self.stats_collector = StatisticsCollector()

        # Configurar tiempo de inicio
        self.config.start_time = time.time()

        bot_logger.info("Bot de WhatsApp inicializado con API oficial")
    
    def validate_config(self) -> bool:
        """Valida la configuración de la API"""
        required_fields = [
            'access_token', 'phone_number_id', 'business_account_id',
            'app_id', 'app_secret', 'webhook_verify_token'
        ]

        missing_fields = []
        for field in required_fields:
            if not getattr(self.config, field, '').strip():
                missing_fields.append(field)

        if missing_fields:
            bot_logger.error(f"Campos de configuración faltantes: {', '.join(missing_fields)}")
            return False

        bot_logger.info("Configuración de API validada correctamente")
        return True
    
    def connect(self) -> bool:
        """Conecta a la API de WhatsApp"""
        try:
            # Validar configuración
            if not self.validate_config():
                return False

            bot_logger.log_connection("Conectando a la API de WhatsApp...")

            # Crear cliente de API
            self.api_client = WhatsAppAPI(self.config)

            # Probar conexión
            if not self.api_client.test_connection():
                bot_logger.error("No se pudo conectar a la API de WhatsApp")
                return False

            # Configurar servidor webhook
            self.webhook_server = WebhookServer(self.config, self._handle_webhook_message)

            self.is_connected = True
            bot_logger.log_connection("Conectado exitosamente a la API de WhatsApp")
            return True

        except Exception as e:
            bot_logger.error(f"Error conectando a la API de WhatsApp: {str(e)}")
            return False
    
    def _is_qr_present(self) -> bool:
        """Verifica si el código QR está presente"""
        try:
            self.driver.find_element(By.CSS_SELECTOR, "[data-ref]")
            return True
        except NoSuchElementException:
            return False
    
    def _is_chat_interface_present(self) -> bool:
        """Verifica si la interfaz de chat está presente"""
        try:
            self.driver.find_element(By.CSS_SELECTOR, "[data-testid='chat-list']")
            return True
        except NoSuchElementException:
            return False
    
    def start_monitoring(self):
        """Inicia el monitoreo de mensajes"""
        if not self.is_connected:
            bot_logger.error("No se puede iniciar monitoreo sin conexión")
            return False
        
        self.is_running = True
        bot_logger.info("Iniciando monitoreo de mensajes...")
        
        # Iniciar hilo de monitoreo
        monitor_thread = threading.Thread(target=self._message_monitor, daemon=True)
        monitor_thread.start()
        
        return True
    
    def stop_monitoring(self):
        """Detiene el monitoreo de mensajes"""
        self.is_running = False
        bot_logger.info("Monitoreo de mensajes detenido")
    
    def _message_monitor(self):
        """Hilo principal de monitoreo de mensajes"""
        while self.is_running and self.is_connected:
            try:
                self._check_new_messages()
                time.sleep(1)  # Pausa para no sobrecargar
                
            except Exception as e:
                bot_logger.error(f"Error en monitoreo de mensajes: {str(e)}")
                time.sleep(5)  # Pausa más larga en caso de error
    
    def _check_new_messages(self):
        """Verifica nuevos mensajes"""
        try:
            # Buscar mensajes no leídos
            unread_chats = self.driver.find_elements(
                By.CSS_SELECTOR, 
                "[data-testid='cell-frame-container'] [data-testid='icon-unread-count']"
            )
            
            for chat_element in unread_chats:
                try:
                    # Hacer clic en el chat
                    chat_container = chat_element.find_element(By.XPATH, "./ancestor::div[@data-testid='cell-frame-container']")
                    chat_container.click()
                    
                    time.sleep(0.5)  # Esperar a que cargue el chat
                    
                    # Procesar mensajes del chat
                    self._process_chat_messages()
                    
                except Exception as e:
                    bot_logger.error(f"Error procesando chat: {str(e)}")
                    continue
                    
        except Exception as e:
            bot_logger.error(f"Error verificando mensajes: {str(e)}")
    
    def _process_chat_messages(self):
        """Procesa mensajes del chat actual"""
        try:
            # Obtener mensajes recientes
            messages = self.driver.find_elements(
                By.CSS_SELECTOR,
                "[data-testid='msg-container']"
            )
            
            # Procesar solo los últimos mensajes
            for message in messages[-5:]:  # Solo los últimos 5 mensajes
                try:
                    self._process_single_message(message)
                except Exception as e:
                    bot_logger.error(f"Error procesando mensaje individual: {str(e)}")
                    continue
                    
        except Exception as e:
            bot_logger.error(f"Error procesando mensajes del chat: {str(e)}")
    
    def _process_single_message(self, message_element):
        """Procesa un mensaje individual"""
        try:
            # Verificar si es mensaje entrante (no enviado por el bot)
            if message_element.find_elements(By.CSS_SELECTOR, "[data-testid='msg-meta'] [data-testid='msg-check']"):
                return  # Es mensaje enviado por nosotros, ignorar
            
            # Obtener texto del mensaje
            text_elements = message_element.find_elements(By.CSS_SELECTOR, "[data-testid='conversation-compose-box-input']")
            if not text_elements:
                text_elements = message_element.find_elements(By.CSS_SELECTOR, "span.selectable-text")
            
            if not text_elements:
                return
            
            message_text = text_elements[0].text.strip()
            if not message_text:
                return
            
            # Obtener información del remitente y chat
            chat_info = self._get_current_chat_info()
            if not chat_info:
                return
            
            # Procesar el mensaje
            self._handle_message(message_text, chat_info)
            
        except Exception as e:
            bot_logger.error(f"Error procesando mensaje individual: {str(e)}")
    
    def _get_current_chat_info(self) -> Optional[Dict[str, Any]]:
        """Obtiene información del chat actual"""
        try:
            # Obtener nombre del chat
            chat_name_element = self.driver.find_element(
                By.CSS_SELECTOR,
                "[data-testid='conversation-header'] [data-testid='conversation-info-header-chat-title']"
            )
            
            chat_name = chat_name_element.text.strip()
            
            # Determinar si es grupo
            is_group = "participants" in self.driver.page_source.lower()
            
            return {
                "name": chat_name,
                "is_group": is_group,
                "id": chat_name  # Simplificado, usar nombre como ID
            }
            
        except Exception as e:
            bot_logger.error(f"Error obteniendo info del chat: {str(e)}")
            return None
    
    def _handle_message(self, message_text: str, chat_info: Dict[str, Any]):
        """Maneja un mensaje recibido"""
        try:
            sender = chat_info["name"]
            chat_id = chat_info["id"]
            is_group = chat_info["is_group"]

            # Registrar estadísticas
            self.stats_collector.record_message_received(sender, is_group)

            # Log del mensaje
            bot_logger.log_message(sender, message_text, "group" if is_group else "private")

            # Verificar si el grupo está permitido
            if is_group and not self.group_manager.is_group_allowed(chat_id):
                return

            # Verificar respuesta automática avanzada
            auto_response = self.auto_responder.get_response(message_text)
            if not auto_response:
                auto_response = self.config.get_auto_response(message_text)

            if auto_response:
                self.send_message(auto_response)
                self.stats_collector.record_message_sent()
                return

            # Verificar si es comando
            if message_text.startswith(self.config.command_prefix):
                # Verificar permisos para comandos en grupos
                if is_group and not self.group_manager.can_use_commands(chat_id, sender):
                    return

                response = self.command_manager.execute_command(
                    message_text, sender, chat_id, is_group
                )
                if response:
                    self.send_message(response)
                    self.stats_collector.record_message_sent()

                    # Registrar comando ejecutado
                    command_name = message_text[1:].split()[0] if len(message_text) > 1 else ""
                    self.stats_collector.record_command_executed(command_name, sender)
                return

            # Llamar handlers personalizados
            for handler_name, handler_func in self.message_handlers.items():
                try:
                    handler_func(message_text, sender, chat_id, is_group)
                except Exception as e:
                    bot_logger.error(f"Error en handler {handler_name}: {str(e)}")
                    self.stats_collector.record_error()

        except Exception as e:
            bot_logger.error(f"Error manejando mensaje: {str(e)}")
            self.stats_collector.record_error()
    
    def send_message(self, message: str) -> bool:
        """Envía un mensaje al chat actual"""
        try:
            # Buscar el campo de texto
            text_box = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "[data-testid='conversation-compose-box-input']"))
            )
            
            # Limpiar y escribir mensaje
            text_box.clear()
            text_box.send_keys(message)
            
            # Buscar y hacer clic en el botón de enviar
            send_button = self.driver.find_element(By.CSS_SELECTOR, "[data-testid='compose-btn-send']")
            send_button.click()
            
            bot_logger.info(f"Mensaje enviado: {message[:50]}...")
            return True
            
        except Exception as e:
            bot_logger.error(f"Error enviando mensaje: {str(e)}")
            return False
    
    def add_message_handler(self, name: str, handler: Callable):
        """Añade un manejador personalizado de mensajes"""
        self.message_handlers[name] = handler
        bot_logger.info(f"Handler de mensajes añadido: {name}")
    
    def remove_message_handler(self, name: str):
        """Remueve un manejador de mensajes"""
        if name in self.message_handlers:
            del self.message_handlers[name]
            bot_logger.info(f"Handler de mensajes removido: {name}")
    
    def disconnect(self):
        """Desconecta el bot"""
        try:
            self.stop_monitoring()
            
            if self.driver:
                self.driver.quit()
                self.driver = None
            
            self.is_connected = False
            bot_logger.log_connection("Bot desconectado")
            
        except Exception as e:
            bot_logger.error(f"Error desconectando bot: {str(e)}")
    
    def __del__(self):
        """Destructor"""
        self.disconnect()
