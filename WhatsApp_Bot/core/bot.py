"""
Núcleo principal del bot de WhatsApp
"""
import time
import json
import os
import threading
from typing import Optional, Dict, Any, Callable
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from selenium.common.exceptions import TimeoutException, NoSuchElementException

from ..config import config
from .logger import bot_logger
from .commands import CommandManager

class WhatsAppBot:
    """Bot principal de WhatsApp"""
    
    def __init__(self):
        self.config = config
        self.driver: Optional[webdriver.Chrome] = None
        self.command_manager = CommandManager(self.config)
        self.is_connected = False
        self.is_running = False
        self.message_handlers: Dict[str, Callable] = {}
        self.last_message_time = 0
        
        # Configurar tiempo de inicio
        self.config.start_time = time.time()
        
        bot_logger.info("Bot de WhatsApp inicializado")
    
    def setup_driver(self):
        """Configura el driver de Chrome"""
        try:
            chrome_options = Options()
            
            # Configuraciones para WhatsApp Web
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--disable-extensions")
            chrome_options.add_argument("--disable-plugins")
            chrome_options.add_argument("--disable-images")
            
            # Directorio de perfil para mantener sesión
            profile_path = os.path.join(self.config.session_path, "chrome_profile")
            os.makedirs(profile_path, exist_ok=True)
            chrome_options.add_argument(f"--user-data-dir={profile_path}")
            
            # Configurar servicio
            service = Service(ChromeDriverManager().install())
            
            # Crear driver
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.maximize_window()
            
            bot_logger.info("Driver de Chrome configurado correctamente")
            return True
            
        except Exception as e:
            bot_logger.error(f"Error configurando driver: {str(e)}")
            return False
    
    def connect(self) -> bool:
        """Conecta a WhatsApp Web"""
        try:
            if not self.setup_driver():
                return False
            
            bot_logger.log_connection("Conectando a WhatsApp Web...")
            self.driver.get("https://web.whatsapp.com")
            
            # Esperar a que aparezca el QR o que ya esté logueado
            try:
                # Esperar por el QR code o por la interfaz principal
                WebDriverWait(self.driver, 30).until(
                    lambda driver: self._is_qr_present() or self._is_chat_interface_present()
                )
                
                if self._is_qr_present():
                    bot_logger.log_connection("Código QR detectado. Escanea con tu teléfono.")
                    
                    # Esperar a que se complete el login
                    WebDriverWait(self.driver, self.config.qr_timeout).until(
                        lambda driver: self._is_chat_interface_present()
                    )
                
                if self._is_chat_interface_present():
                    self.is_connected = True
                    bot_logger.log_connection("Conectado exitosamente a WhatsApp Web")
                    return True
                
            except TimeoutException:
                bot_logger.error("Timeout esperando conexión a WhatsApp Web")
                return False
                
        except Exception as e:
            bot_logger.error(f"Error conectando a WhatsApp Web: {str(e)}")
            return False
    
    def _is_qr_present(self) -> bool:
        """Verifica si el código QR está presente"""
        try:
            self.driver.find_element(By.CSS_SELECTOR, "[data-ref]")
            return True
        except NoSuchElementException:
            return False
    
    def _is_chat_interface_present(self) -> bool:
        """Verifica si la interfaz de chat está presente"""
        try:
            self.driver.find_element(By.CSS_SELECTOR, "[data-testid='chat-list']")
            return True
        except NoSuchElementException:
            return False
    
    def start_monitoring(self):
        """Inicia el monitoreo de mensajes"""
        if not self.is_connected:
            bot_logger.error("No se puede iniciar monitoreo sin conexión")
            return False
        
        self.is_running = True
        bot_logger.info("Iniciando monitoreo de mensajes...")
        
        # Iniciar hilo de monitoreo
        monitor_thread = threading.Thread(target=self._message_monitor, daemon=True)
        monitor_thread.start()
        
        return True
    
    def stop_monitoring(self):
        """Detiene el monitoreo de mensajes"""
        self.is_running = False
        bot_logger.info("Monitoreo de mensajes detenido")
    
    def _message_monitor(self):
        """Hilo principal de monitoreo de mensajes"""
        while self.is_running and self.is_connected:
            try:
                self._check_new_messages()
                time.sleep(1)  # Pausa para no sobrecargar
                
            except Exception as e:
                bot_logger.error(f"Error en monitoreo de mensajes: {str(e)}")
                time.sleep(5)  # Pausa más larga en caso de error
    
    def _check_new_messages(self):
        """Verifica nuevos mensajes"""
        try:
            # Buscar mensajes no leídos
            unread_chats = self.driver.find_elements(
                By.CSS_SELECTOR, 
                "[data-testid='cell-frame-container'] [data-testid='icon-unread-count']"
            )
            
            for chat_element in unread_chats:
                try:
                    # Hacer clic en el chat
                    chat_container = chat_element.find_element(By.XPATH, "./ancestor::div[@data-testid='cell-frame-container']")
                    chat_container.click()
                    
                    time.sleep(0.5)  # Esperar a que cargue el chat
                    
                    # Procesar mensajes del chat
                    self._process_chat_messages()
                    
                except Exception as e:
                    bot_logger.error(f"Error procesando chat: {str(e)}")
                    continue
                    
        except Exception as e:
            bot_logger.error(f"Error verificando mensajes: {str(e)}")
    
    def _process_chat_messages(self):
        """Procesa mensajes del chat actual"""
        try:
            # Obtener mensajes recientes
            messages = self.driver.find_elements(
                By.CSS_SELECTOR,
                "[data-testid='msg-container']"
            )
            
            # Procesar solo los últimos mensajes
            for message in messages[-5:]:  # Solo los últimos 5 mensajes
                try:
                    self._process_single_message(message)
                except Exception as e:
                    bot_logger.error(f"Error procesando mensaje individual: {str(e)}")
                    continue
                    
        except Exception as e:
            bot_logger.error(f"Error procesando mensajes del chat: {str(e)}")
    
    def _process_single_message(self, message_element):
        """Procesa un mensaje individual"""
        try:
            # Verificar si es mensaje entrante (no enviado por el bot)
            if message_element.find_elements(By.CSS_SELECTOR, "[data-testid='msg-meta'] [data-testid='msg-check']"):
                return  # Es mensaje enviado por nosotros, ignorar
            
            # Obtener texto del mensaje
            text_elements = message_element.find_elements(By.CSS_SELECTOR, "[data-testid='conversation-compose-box-input']")
            if not text_elements:
                text_elements = message_element.find_elements(By.CSS_SELECTOR, "span.selectable-text")
            
            if not text_elements:
                return
            
            message_text = text_elements[0].text.strip()
            if not message_text:
                return
            
            # Obtener información del remitente y chat
            chat_info = self._get_current_chat_info()
            if not chat_info:
                return
            
            # Procesar el mensaje
            self._handle_message(message_text, chat_info)
            
        except Exception as e:
            bot_logger.error(f"Error procesando mensaje individual: {str(e)}")
    
    def _get_current_chat_info(self) -> Optional[Dict[str, Any]]:
        """Obtiene información del chat actual"""
        try:
            # Obtener nombre del chat
            chat_name_element = self.driver.find_element(
                By.CSS_SELECTOR,
                "[data-testid='conversation-header'] [data-testid='conversation-info-header-chat-title']"
            )
            
            chat_name = chat_name_element.text.strip()
            
            # Determinar si es grupo
            is_group = "participants" in self.driver.page_source.lower()
            
            return {
                "name": chat_name,
                "is_group": is_group,
                "id": chat_name  # Simplificado, usar nombre como ID
            }
            
        except Exception as e:
            bot_logger.error(f"Error obteniendo info del chat: {str(e)}")
            return None
    
    def _handle_message(self, message_text: str, chat_info: Dict[str, Any]):
        """Maneja un mensaje recibido"""
        try:
            sender = chat_info["name"]
            chat_id = chat_info["id"]
            is_group = chat_info["is_group"]
            
            # Log del mensaje
            bot_logger.log_message(sender, message_text, "group" if is_group else "private")
            
            # Verificar respuesta automática
            auto_response = self.config.get_auto_response(message_text)
            if auto_response:
                self.send_message(auto_response)
                return
            
            # Verificar si es comando
            if message_text.startswith(self.config.command_prefix):
                response = self.command_manager.execute_command(
                    message_text, sender, chat_id, is_group
                )
                if response:
                    self.send_message(response)
                return
            
            # Llamar handlers personalizados
            for handler_name, handler_func in self.message_handlers.items():
                try:
                    handler_func(message_text, sender, chat_id, is_group)
                except Exception as e:
                    bot_logger.error(f"Error en handler {handler_name}: {str(e)}")
                    
        except Exception as e:
            bot_logger.error(f"Error manejando mensaje: {str(e)}")
    
    def send_message(self, message: str) -> bool:
        """Envía un mensaje al chat actual"""
        try:
            # Buscar el campo de texto
            text_box = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "[data-testid='conversation-compose-box-input']"))
            )
            
            # Limpiar y escribir mensaje
            text_box.clear()
            text_box.send_keys(message)
            
            # Buscar y hacer clic en el botón de enviar
            send_button = self.driver.find_element(By.CSS_SELECTOR, "[data-testid='compose-btn-send']")
            send_button.click()
            
            bot_logger.info(f"Mensaje enviado: {message[:50]}...")
            return True
            
        except Exception as e:
            bot_logger.error(f"Error enviando mensaje: {str(e)}")
            return False
    
    def add_message_handler(self, name: str, handler: Callable):
        """Añade un manejador personalizado de mensajes"""
        self.message_handlers[name] = handler
        bot_logger.info(f"Handler de mensajes añadido: {name}")
    
    def remove_message_handler(self, name: str):
        """Remueve un manejador de mensajes"""
        if name in self.message_handlers:
            del self.message_handlers[name]
            bot_logger.info(f"Handler de mensajes removido: {name}")
    
    def disconnect(self):
        """Desconecta el bot"""
        try:
            self.stop_monitoring()
            
            if self.driver:
                self.driver.quit()
                self.driver = None
            
            self.is_connected = False
            bot_logger.log_connection("Bot desconectado")
            
        except Exception as e:
            bot_logger.error(f"Error desconectando bot: {str(e)}")
    
    def __del__(self):
        """Destructor"""
        self.disconnect()
