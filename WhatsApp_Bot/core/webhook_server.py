"""
Servidor webhook para recibir mensajes de WhatsApp
"""
import json
import threading
from datetime import datetime
from typing import Dict, Any, Callable, Optional
from aiohttp import web, ClientSession
import asyncio
from .logger import bot_logger

class WebhookServer:
    """Servidor webhook para WhatsApp"""
    
    def __init__(self, config, message_handler: Callable = None):
        self.config = config
        self.message_handler = message_handler
        self.app = web.Application()
        self.runner = None
        self.site = None
        self.is_running = False
        
        # Configurar rutas
        self.app.router.add_get('/webhook', self.verify_webhook)
        self.app.router.add_post('/webhook', self.handle_webhook)
        self.app.router.add_get('/health', self.health_check)
        
    async def verify_webhook(self, request):
        """Verifica el webhook de WhatsApp"""
        try:
            mode = request.query.get('hub.mode')
            token = request.query.get('hub.verify_token')
            challenge = request.query.get('hub.challenge')
            
            if mode == 'subscribe' and token == self.config.webhook_verify_token:
                bot_logger.info("Webhook verificado exitosamente")
                return web.Response(text=challenge, status=200)
            else:
                bot_logger.warning("Verificación de webhook fallida")
                return web.Response(text='Forbidden', status=403)
                
        except Exception as e:
            bot_logger.error(f"Error en verificación de webhook: {e}")
            return web.Response(text='Error', status=500)
    
    async def handle_webhook(self, request):
        """Maneja los webhooks entrantes"""
        try:
            # Verificar firma si está configurada
            if self.config.app_secret:
                signature = request.headers.get('X-Hub-Signature-256', '')
                body = await request.text()
                
                if not self._verify_signature(body, signature):
                    bot_logger.warning("Firma de webhook inválida")
                    return web.Response(text='Forbidden', status=403)
            
            # Procesar datos
            data = await request.json()
            
            if self.config.debug:
                bot_logger.debug(f"Webhook recibido: {json.dumps(data, indent=2)}")
            
            # Procesar entradas
            for entry in data.get('entry', []):
                await self._process_entry(entry)
            
            return web.Response(text='OK', status=200)
            
        except Exception as e:
            bot_logger.error(f"Error procesando webhook: {e}")
            return web.Response(text='Error', status=500)
    
    async def health_check(self, request):
        """Endpoint de salud"""
        return web.json_response({
            'status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'bot_name': self.config.bot_name,
            'version': self.config.version
        })
    
    def _verify_signature(self, payload: str, signature: str) -> bool:
        """Verifica la firma del webhook"""
        import hmac
        import hashlib
        
        try:
            expected_signature = hmac.new(
                self.config.app_secret.encode('utf-8'),
                payload.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()
            
            return hmac.compare_digest(f"sha256={expected_signature}", signature)
        except Exception as e:
            bot_logger.error(f"Error verificando firma: {e}")
            return False
    
    async def _process_entry(self, entry: Dict[str, Any]):
        """Procesa una entrada del webhook"""
        try:
            for change in entry.get('changes', []):
                field = change.get('field')
                value = change.get('value', {})
                
                if field == 'messages':
                    await self._process_messages(value)
                elif field == 'message_status':
                    await self._process_message_status(value)
                
        except Exception as e:
            bot_logger.error(f"Error procesando entrada: {e}")
    
    async def _process_messages(self, value: Dict[str, Any]):
        """Procesa mensajes entrantes"""
        try:
            messages = value.get('messages', [])
            contacts = value.get('contacts', [])
            
            # Crear mapa de contactos
            contact_map = {contact['wa_id']: contact for contact in contacts}
            
            for message in messages:
                await self._handle_incoming_message(message, contact_map)
                
        except Exception as e:
            bot_logger.error(f"Error procesando mensajes: {e}")
    
    async def _handle_incoming_message(self, message: Dict[str, Any], contact_map: Dict[str, Any]):
        """Maneja un mensaje entrante individual"""
        try:
            message_id = message.get('id')
            from_number = message.get('from')
            timestamp = message.get('timestamp')
            message_type = message.get('type')
            
            # Obtener información del contacto
            contact = contact_map.get(from_number, {})
            contact_name = contact.get('profile', {}).get('name', from_number)
            
            # Extraer contenido del mensaje
            message_content = self._extract_message_content(message, message_type)
            
            if not message_content:
                return
            
            # Crear objeto de mensaje normalizado
            normalized_message = {
                'id': message_id,
                'from': from_number,
                'from_name': contact_name,
                'timestamp': datetime.fromtimestamp(int(timestamp)),
                'type': message_type,
                'content': message_content,
                'is_group': False,  # La API no maneja grupos directamente
                'chat_id': from_number
            }
            
            # Llamar al manejador de mensajes si existe
            if self.message_handler:
                try:
                    await self._call_message_handler(normalized_message)
                except Exception as e:
                    bot_logger.error(f"Error en manejador de mensajes: {e}")
            
            bot_logger.info(f"Mensaje procesado de {contact_name}: {message_content[:50]}...")
            
        except Exception as e:
            bot_logger.error(f"Error manejando mensaje entrante: {e}")
    
    def _extract_message_content(self, message: Dict[str, Any], message_type: str) -> Optional[str]:
        """Extrae el contenido del mensaje según su tipo"""
        try:
            if message_type == 'text':
                return message.get('text', {}).get('body', '')
            
            elif message_type == 'button':
                return message.get('button', {}).get('text', '')
            
            elif message_type == 'interactive':
                interactive = message.get('interactive', {})
                if interactive.get('type') == 'button_reply':
                    return interactive.get('button_reply', {}).get('title', '')
                elif interactive.get('type') == 'list_reply':
                    return interactive.get('list_reply', {}).get('title', '')
            
            elif message_type in ['image', 'video', 'audio', 'document']:
                media = message.get(message_type, {})
                caption = media.get('caption', '')
                return f"[{message_type.upper()}] {caption}" if caption else f"[{message_type.upper()}]"
            
            elif message_type == 'location':
                location = message.get('location', {})
                return f"[UBICACIÓN] Lat: {location.get('latitude')}, Lng: {location.get('longitude')}"
            
            elif message_type == 'contacts':
                contacts = message.get('contacts', [])
                if contacts:
                    contact = contacts[0]
                    name = contact.get('name', {}).get('formatted_name', 'Contacto')
                    return f"[CONTACTO] {name}"
            
            return f"[{message_type.upper()}]"
            
        except Exception as e:
            bot_logger.error(f"Error extrayendo contenido del mensaje: {e}")
            return None
    
    async def _call_message_handler(self, message: Dict[str, Any]):
        """Llama al manejador de mensajes"""
        try:
            if asyncio.iscoroutinefunction(self.message_handler):
                await self.message_handler(message)
            else:
                # Ejecutar en hilo separado si no es async
                loop = asyncio.get_event_loop()
                await loop.run_in_executor(None, self.message_handler, message)
                
        except Exception as e:
            bot_logger.error(f"Error llamando manejador de mensajes: {e}")
    
    async def _process_message_status(self, value: Dict[str, Any]):
        """Procesa actualizaciones de estado de mensajes"""
        try:
            statuses = value.get('statuses', [])
            
            for status in statuses:
                message_id = status.get('id')
                status_type = status.get('status')
                timestamp = status.get('timestamp')
                recipient_id = status.get('recipient_id')
                
                bot_logger.debug(f"Estado de mensaje {message_id}: {status_type}")
                
                # Aquí puedes agregar lógica para manejar estados específicos
                # como 'sent', 'delivered', 'read', 'failed'
                
        except Exception as e:
            bot_logger.error(f"Error procesando estado de mensaje: {e}")
    
    async def start(self):
        """Inicia el servidor webhook"""
        try:
            self.runner = web.AppRunner(self.app)
            await self.runner.setup()
            
            self.site = web.TCPSite(
                self.runner, 
                '0.0.0.0', 
                self.config.webhook_port
            )
            
            await self.site.start()
            self.is_running = True
            
            bot_logger.info(f"Servidor webhook iniciado en puerto {self.config.webhook_port}")
            
        except Exception as e:
            bot_logger.error(f"Error iniciando servidor webhook: {e}")
            raise
    
    async def stop(self):
        """Detiene el servidor webhook"""
        try:
            self.is_running = False
            
            if self.site:
                await self.site.stop()
                self.site = None
            
            if self.runner:
                await self.runner.cleanup()
                self.runner = None
            
            bot_logger.info("Servidor webhook detenido")
            
        except Exception as e:
            bot_logger.error(f"Error deteniendo servidor webhook: {e}")
    
    def run_in_thread(self):
        """Ejecuta el servidor en un hilo separado"""
        def run_server():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                loop.run_until_complete(self.start())
                loop.run_forever()
            except Exception as e:
                bot_logger.error(f"Error en hilo del servidor: {e}")
            finally:
                loop.run_until_complete(self.stop())
                loop.close()
        
        thread = threading.Thread(target=run_server, daemon=True)
        thread.start()
        return thread
