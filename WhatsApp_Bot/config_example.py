"""
Ejemplo de configuración para el Bot de WhatsApp
Copia este archivo como 'config_local.py' y completa tus datos
"""

# ========================================
# CONFIGURACIÓN DE LA API DE WHATSAPP
# ========================================

# Token de acceso de la API de WhatsApp Business
# Obtenerlo desde: https://developers.facebook.com/apps/
ACCESS_TOKEN = "tu_access_token_aqui"

# ID del número de teléfono de WhatsApp Business
# Formato: ***************
PHONE_NUMBER_ID = "tu_phone_number_id_aqui"

# ID de la cuenta de negocio de WhatsApp
BUSINESS_ACCOUNT_ID = "tu_business_account_id_aqui"

# ID de la aplicación de Facebook
APP_ID = "tu_app_id_aqui"

# Secreto de la aplicación de Facebook
APP_SECRET = "tu_app_secret_aqui"

# Token de verificación del webhook (puedes usar cualquier string)
WEBHOOK_VERIFY_TOKEN = "mi_token_secreto_123"

# URL pública de tu webhook (debe ser HTTPS)
# Ejemplo: https://tu-dominio.com/webhook
WEBHOOK_URL = "https://tu-dominio.com/webhook"

# Puerto para el servidor webhook local
WEBHOOK_PORT = 8000

# ========================================
# CONFIGURACIÓN DEL BOT
# ========================================

# Nombre del bot
BOT_NAME = "Mi Bot de WhatsApp"

# Prefijo para comandos
COMMAND_PREFIX = "!"

# Habilitar modo debug
DEBUG = True

# Números de administradores (formato internacional sin +)
# Ejemplo: ["**********", "**********"]
ADMIN_NUMBERS = [
    "tu_numero_aqui"
]

# ========================================
# RESPUESTAS AUTOMÁTICAS
# ========================================

AUTO_RESPONSES = {
    "hola": "¡Hola! Soy tu bot de WhatsApp. Escribe !help para ver los comandos disponibles.",
    "buenos días": "¡Buenos días! ¿En qué puedo ayudarte hoy?",
    "buenas tardes": "¡Buenas tardes! ¿Cómo estás?",
    "buenas noches": "¡Buenas noches! Que descanses bien.",
    "gracias": "¡De nada! Siempre a tu servicio 😊",
    "help": "Comandos disponibles:\n!ping - Verificar conexión\n!info - Información del bot\n!time - Hora actual\n!help - Esta ayuda"
}

# ========================================
# CONFIGURACIÓN DE GRUPOS
# ========================================

# Habilitar comandos en grupos
GROUP_COMMANDS_ENABLED = True

# Lista de grupos permitidos (vacío = todos permitidos)
# Usar el ID del grupo o nombre
ALLOWED_GROUPS = []

# ========================================
# CONFIGURACIÓN DE LOGS
# ========================================

# Nivel de logs: DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_LEVEL = "INFO"

# ========================================
# CONFIGURACIÓN DE LA GUI
# ========================================

# Habilitar interfaz gráfica
GUI_ENABLED = True

# Tema de la GUI: "light" o "dark"
GUI_THEME = "dark"

# ========================================
# FUNCIÓN PARA APLICAR CONFIGURACIÓN
# ========================================

def apply_config():
    """Aplica la configuración al bot"""
    from WhatsApp_Bot import config
    
    # Configuración de API
    config.access_token = ACCESS_TOKEN
    config.phone_number_id = PHONE_NUMBER_ID
    config.business_account_id = BUSINESS_ACCOUNT_ID
    config.app_id = APP_ID
    config.app_secret = APP_SECRET
    config.webhook_verify_token = WEBHOOK_VERIFY_TOKEN
    config.webhook_url = WEBHOOK_URL
    config.webhook_port = WEBHOOK_PORT
    
    # Configuración del bot
    config.bot_name = BOT_NAME
    config.command_prefix = COMMAND_PREFIX
    config.debug = DEBUG
    config.admin_numbers = ADMIN_NUMBERS.copy()
    
    # Respuestas automáticas
    config.auto_responses.update(AUTO_RESPONSES)
    
    # Configuración de grupos
    config.group_commands_enabled = GROUP_COMMANDS_ENABLED
    config.allowed_groups = ALLOWED_GROUPS.copy()
    
    # Configuración de logs
    config.log_level = LOG_LEVEL
    
    # Configuración de GUI
    config.gui_enabled = GUI_ENABLED
    config.gui_theme = GUI_THEME
    
    print("✅ Configuración aplicada exitosamente")

# ========================================
# EJEMPLO DE USO
# ========================================

if __name__ == "__main__":
    print("📋 Configuración de ejemplo del Bot de WhatsApp")
    print("=" * 50)
    print()
    print("Para usar esta configuración:")
    print("1. Copia este archivo como 'config_local.py'")
    print("2. Completa todos los campos con tus datos reales")
    print("3. Importa y aplica la configuración:")
    print()
    print("   from config_local import apply_config")
    print("   apply_config()")
    print()
    print("📚 Guía para obtener las credenciales:")
    print("1. Ve a https://developers.facebook.com/apps/")
    print("2. Crea una nueva aplicación")
    print("3. Añade el producto 'WhatsApp Business API'")
    print("4. Configura tu número de teléfono")
    print("5. Obtén tu token de acceso")
    print("6. Configura el webhook")
    print()
    print("🔗 Documentación oficial:")
    print("https://developers.facebook.com/docs/whatsapp/business-management-api/get-started")
