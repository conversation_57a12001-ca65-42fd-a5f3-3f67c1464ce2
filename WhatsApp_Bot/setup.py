"""
Script de instalación y configuración del Bot de WhatsApp
"""
import os
import sys
import subprocess
import shutil
from pathlib import Path

def print_banner():
    """Muestra el banner de instalación"""
    print("=" * 60)
    print("🤖 BOT DE WHATSAPP - ONEFORALL PLUGIN")
    print("=" * 60)
    print("Instalador y configurador automático")
    print("Versión: 1.0.0")
    print("=" * 60)
    print()

def check_python_version():
    """Verifica la versión de Python"""
    print("🔍 Verificando versión de Python...")
    
    if sys.version_info < (3, 8):
        print("❌ Error: Se requiere Python 3.8 o superior")
        print(f"   Versión actual: {sys.version}")
        return False
    
    print(f"✅ Python {sys.version.split()[0]} - OK")
    return True

def install_dependencies():
    """Instala las dependencias necesarias"""
    print("\n📦 Instalando dependencias...")
    
    requirements_file = Path(__file__).parent / "requirements.txt"
    
    if not requirements_file.exists():
        print("❌ Error: Archivo requirements.txt no encontrado")
        return False
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", str(requirements_file)
        ])
        print("✅ Dependencias instaladas correctamente")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Error instalando dependencias: {e}")
        return False

def create_config_file():
    """Crea el archivo de configuración"""
    print("\n⚙️ Configurando el bot...")
    
    config_example = Path(__file__).parent / "config_example.py"
    config_local = Path(__file__).parent / "config_local.py"
    
    if config_local.exists():
        overwrite = input("📄 Ya existe config_local.py. ¿Sobrescribir? (s/N): ").lower()
        if overwrite != 's':
            print("✅ Manteniendo configuración existente")
            return True
    
    if not config_example.exists():
        print("❌ Error: config_example.py no encontrado")
        return False
    
    try:
        shutil.copy2(config_example, config_local)
        print("✅ Archivo de configuración creado: config_local.py")
        return True
        
    except Exception as e:
        print(f"❌ Error creando configuración: {e}")
        return False

def setup_directories():
    """Crea los directorios necesarios"""
    print("\n📁 Creando directorios...")
    
    base_path = Path(__file__).parent
    directories = [
        "logs",
        "session", 
        "media",
        "backups"
    ]
    
    for directory in directories:
        dir_path = base_path / directory
        try:
            dir_path.mkdir(exist_ok=True)
            print(f"✅ Directorio creado: {directory}/")
        except Exception as e:
            print(f"❌ Error creando {directory}/: {e}")
            return False
    
    return True

def interactive_config():
    """Configuración interactiva"""
    print("\n🔧 Configuración interactiva")
    print("=" * 40)
    
    configure = input("¿Deseas configurar el bot ahora? (s/N): ").lower()
    if configure != 's':
        print("⏭️ Configuración omitida. Puedes configurar más tarde editando config_local.py")
        return True
    
    config_data = {}
    
    print("\n📱 Configuración de la API de WhatsApp:")
    print("(Obtén estos datos desde https://developers.facebook.com/apps/)")
    
    config_data['access_token'] = input("🔑 Access Token: ").strip()
    config_data['phone_number_id'] = input("📞 Phone Number ID: ").strip()
    config_data['business_account_id'] = input("🏢 Business Account ID: ").strip()
    config_data['app_id'] = input("📱 App ID: ").strip()
    config_data['app_secret'] = input("🔐 App Secret: ").strip()
    config_data['webhook_verify_token'] = input("🔗 Webhook Verify Token: ").strip()
    config_data['webhook_url'] = input("🌐 Webhook URL: ").strip()
    
    print("\n🤖 Configuración del bot:")
    config_data['bot_name'] = input("📝 Nombre del bot [Mi Bot de WhatsApp]: ").strip() or "Mi Bot de WhatsApp"
    config_data['command_prefix'] = input("⚡ Prefijo de comandos [!]: ").strip() or "!"
    
    admin_number = input("👤 Tu número de WhatsApp (formato internacional sin +): ").strip()
    if admin_number:
        config_data['admin_numbers'] = [admin_number]
    
    # Escribir configuración
    try:
        config_local = Path(__file__).parent / "config_local.py"
        
        with open(config_local, 'w', encoding='utf-8') as f:
            f.write('"""\nConfiguración del Bot de WhatsApp\nGenerado automáticamente\n"""\n\n')
            
            f.write("# Configuración de la API\n")
            f.write(f'ACCESS_TOKEN = "{config_data.get("access_token", "")}"\n')
            f.write(f'PHONE_NUMBER_ID = "{config_data.get("phone_number_id", "")}"\n')
            f.write(f'BUSINESS_ACCOUNT_ID = "{config_data.get("business_account_id", "")}"\n')
            f.write(f'APP_ID = "{config_data.get("app_id", "")}"\n')
            f.write(f'APP_SECRET = "{config_data.get("app_secret", "")}"\n')
            f.write(f'WEBHOOK_VERIFY_TOKEN = "{config_data.get("webhook_verify_token", "")}"\n')
            f.write(f'WEBHOOK_URL = "{config_data.get("webhook_url", "")}"\n')
            f.write('WEBHOOK_PORT = 8000\n\n')
            
            f.write("# Configuración del bot\n")
            f.write(f'BOT_NAME = "{config_data.get("bot_name", "Mi Bot de WhatsApp")}"\n')
            f.write(f'COMMAND_PREFIX = "{config_data.get("command_prefix", "!")}"\n')
            f.write('DEBUG = True\n')
            
            admin_numbers = config_data.get('admin_numbers', [])
            f.write(f'ADMIN_NUMBERS = {admin_numbers}\n\n')
            
            f.write("# Respuestas automáticas básicas\n")
            f.write('AUTO_RESPONSES = {\n')
            f.write('    "hola": "¡Hola! Soy tu bot de WhatsApp. Escribe !help para ver comandos.",\n')
            f.write('    "help": "Comandos: !ping, !info, !time, !help"\n')
            f.write('}\n\n')
            
            f.write("# Configuración adicional\n")
            f.write('GROUP_COMMANDS_ENABLED = True\n')
            f.write('ALLOWED_GROUPS = []\n')
            f.write('LOG_LEVEL = "INFO"\n')
            f.write('GUI_ENABLED = True\n')
            f.write('GUI_THEME = "dark"\n\n')
            
            # Función apply_config
            f.write('def apply_config():\n')
            f.write('    """Aplica la configuración al bot"""\n')
            f.write('    from WhatsApp_Bot import config\n')
            f.write('    \n')
            f.write('    config.access_token = ACCESS_TOKEN\n')
            f.write('    config.phone_number_id = PHONE_NUMBER_ID\n')
            f.write('    config.business_account_id = BUSINESS_ACCOUNT_ID\n')
            f.write('    config.app_id = APP_ID\n')
            f.write('    config.app_secret = APP_SECRET\n')
            f.write('    config.webhook_verify_token = WEBHOOK_VERIFY_TOKEN\n')
            f.write('    config.webhook_url = WEBHOOK_URL\n')
            f.write('    config.webhook_port = WEBHOOK_PORT\n')
            f.write('    \n')
            f.write('    config.bot_name = BOT_NAME\n')
            f.write('    config.command_prefix = COMMAND_PREFIX\n')
            f.write('    config.debug = DEBUG\n')
            f.write('    config.admin_numbers = ADMIN_NUMBERS.copy()\n')
            f.write('    \n')
            f.write('    config.auto_responses.update(AUTO_RESPONSES)\n')
            f.write('    config.group_commands_enabled = GROUP_COMMANDS_ENABLED\n')
            f.write('    config.allowed_groups = ALLOWED_GROUPS.copy()\n')
            f.write('    config.log_level = LOG_LEVEL\n')
            f.write('    config.gui_enabled = GUI_ENABLED\n')
            f.write('    config.gui_theme = GUI_THEME\n')
        
        print("✅ Configuración guardada en config_local.py")
        return True
        
    except Exception as e:
        print(f"❌ Error guardando configuración: {e}")
        return False

def create_launcher_script():
    """Crea script de lanzamiento"""
    print("\n🚀 Creando script de lanzamiento...")
    
    launcher_content = '''#!/usr/bin/env python3
"""
Lanzador del Bot de WhatsApp
"""
import sys
import os

# Añadir el directorio del bot al path
bot_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, bot_dir)

try:
    # Importar configuración local si existe
    try:
        from config_local import apply_config
        apply_config()
        print("✅ Configuración local aplicada")
    except ImportError:
        print("⚠️ No se encontró config_local.py, usando configuración por defecto")
    
    # Iniciar el bot
    from WhatsApp_Bot import main
    
    print("🤖 Iniciando Bot de WhatsApp...")
    main()
    
except KeyboardInterrupt:
    print("\\n👋 Bot detenido por el usuario")
except Exception as e:
    print(f"❌ Error iniciando el bot: {e}")
    input("Presiona Enter para salir...")
'''
    
    try:
        launcher_path = Path(__file__).parent / "run_bot.py"
        with open(launcher_path, 'w', encoding='utf-8') as f:
            f.write(launcher_content)
        
        # Hacer ejecutable en sistemas Unix
        if os.name != 'nt':
            os.chmod(launcher_path, 0o755)
        
        print("✅ Script de lanzamiento creado: run_bot.py")
        return True
        
    except Exception as e:
        print(f"❌ Error creando launcher: {e}")
        return False

def show_completion_message():
    """Muestra mensaje de finalización"""
    print("\n" + "=" * 60)
    print("🎉 ¡INSTALACIÓN COMPLETADA!")
    print("=" * 60)
    print()
    print("📋 Próximos pasos:")
    print("1. Configura tu webhook en Facebook Developers")
    print("2. Ejecuta el bot con: python run_bot.py")
    print("3. O usa la GUI: python -m WhatsApp_Bot.gui.main_window")
    print()
    print("📚 Documentación:")
    print("- README.md - Guía completa")
    print("- config_local.py - Tu configuración")
    print("- ejemplo_uso.py - Ejemplos de uso")
    print()
    print("🆘 Soporte:")
    print("- Revisa los logs en logs/bot.log")
    print("- Consulta la documentación oficial de WhatsApp API")
    print()
    print("¡Disfruta tu bot de WhatsApp! 🤖✨")
    print("=" * 60)

def main():
    """Función principal de instalación"""
    print_banner()
    
    # Verificar Python
    if not check_python_version():
        return False
    
    # Instalar dependencias
    if not install_dependencies():
        return False
    
    # Crear directorios
    if not setup_directories():
        return False
    
    # Crear configuración
    if not create_config_file():
        return False
    
    # Configuración interactiva
    if not interactive_config():
        return False
    
    # Crear launcher
    if not create_launcher_script():
        return False
    
    # Mensaje final
    show_completion_message()
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            print("\n❌ La instalación falló. Revisa los errores anteriores.")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n⏹️ Instalación cancelada por el usuario")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Error inesperado: {e}")
        sys.exit(1)
