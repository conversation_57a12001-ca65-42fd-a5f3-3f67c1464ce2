"""
Ejemplo de uso del bot de WhatsApp
"""

from WhatsApp_Bot import WhatsAppBot, config, main

def ejemplo_basico():
    """Ejemplo básico de uso del bot"""
    print("=== Ejemplo Básico del Bot de WhatsApp ===")
    
    # Configurar el bot
    config.bot_name = "Mi Bot Personal"
    config.command_prefix = "!"
    config.debug = True
    
    # Añadir administradores
    config.add_admin("1234567890")  # Reemplazar con tu número
    
    # Crear instancia del bot
    bot = WhatsAppBot()
    
    # Añadir respuestas automáticas personalizadas
    config.add_auto_response("buenos días", "¡Buenos días! ¿En qué puedo ayudarte?")
    config.add_auto_response("gracias", "¡De nada! Siempre a tu servicio 😊")
    
    # Añadir un manejador personalizado de mensajes
    def mi_manejador_personalizado(mensaje, remitente, chat_id, es_grupo):
        """Manejador personalizado para mensajes específicos"""
        if "python" in mensaje.lower():
            bot.send_message("¡Python es genial! 🐍")
        elif "bot" in mensaje.lower():
            bot.send_message("¡Soy un bot muy inteligente! 🤖")
    
    # Registrar el manejador
    bot.add_message_handler("respuestas_python", mi_manejador_personalizado)
    
    # Registrar comando personalizado
    def comando_saludo(args, sender, chat_id, is_group):
        """Comando personalizado de saludo"""
        nombre = args[0] if args else "amigo"
        return f"¡Hola {nombre}! Bienvenido al bot de WhatsApp 👋"
    
    bot.command_manager.register_command(
        "saludo", 
        "Saluda a una persona", 
        comando_saludo
    )
    
    print("Bot configurado. Usa la GUI para conectar y controlar el bot.")
    print("Comandos disponibles:")
    print("- !ping - Verificar conexión")
    print("- !info - Información del bot")
    print("- !time - Hora actual")
    print("- !help - Lista de comandos")
    print("- !saludo [nombre] - Saludo personalizado")
    print("- !status - Estado del bot (solo admin)")
    
    return bot

def ejemplo_con_gui():
    """Ejemplo usando la interfaz gráfica"""
    print("=== Iniciando Bot con Interfaz Gráfica ===")
    
    # Configurar antes de iniciar la GUI
    config.bot_name = "WhatsApp Bot GUI"
    config.gui_enabled = True
    config.gui_theme = "dark"
    
    # Iniciar la GUI
    main()

def ejemplo_programatico():
    """Ejemplo de uso programático sin GUI"""
    print("=== Ejemplo Programático ===")
    
    # Crear bot
    bot = WhatsAppBot()
    
    try:
        # Conectar
        print("Conectando al bot...")
        if bot.connect():
            print("✅ Bot conectado exitosamente")
            
            # Iniciar monitoreo
            if bot.start_monitoring():
                print("✅ Monitoreo iniciado")
                
                # Mantener el bot corriendo
                print("Bot funcionando. Presiona Ctrl+C para detener.")
                import time
                while True:
                    time.sleep(1)
            else:
                print("❌ Error iniciando monitoreo")
        else:
            print("❌ Error conectando bot")
            
    except KeyboardInterrupt:
        print("\n🛑 Deteniendo bot...")
        bot.disconnect()
        print("✅ Bot desconectado")
    except Exception as e:
        print(f"❌ Error: {e}")
        bot.disconnect()

def mostrar_configuracion():
    """Muestra la configuración actual"""
    print("=== Configuración Actual ===")
    print(f"Nombre del bot: {config.bot_name}")
    print(f"Versión: {config.version}")
    print(f"Prefijo de comandos: {config.command_prefix}")
    print(f"Debug habilitado: {config.debug}")
    print(f"Comandos en grupos: {config.group_commands_enabled}")
    print(f"Administradores: {config.admin_numbers}")
    print(f"Respuestas automáticas: {len(config.auto_responses)}")
    print(f"Ruta de sesión: {config.session_path}")
    print(f"Archivo de logs: {config.log_file}")

if __name__ == "__main__":
    print("🤖 Bot de WhatsApp - OneForAll Plugin")
    print("=====================================")
    
    # Mostrar configuración
    mostrar_configuracion()
    print()
    
    # Menú de opciones
    print("Opciones disponibles:")
    print("1. Ejemplo básico (configuración)")
    print("2. Iniciar con interfaz gráfica")
    print("3. Ejemplo programático (sin GUI)")
    print("4. Solo mostrar configuración")
    
    try:
        opcion = input("\nSelecciona una opción (1-4): ").strip()
        
        if opcion == "1":
            bot = ejemplo_basico()
        elif opcion == "2":
            ejemplo_con_gui()
        elif opcion == "3":
            ejemplo_programatico()
        elif opcion == "4":
            mostrar_configuracion()
        else:
            print("Opción no válida. Iniciando GUI por defecto...")
            ejemplo_con_gui()
            
    except KeyboardInterrupt:
        print("\n👋 ¡Hasta luego!")
    except Exception as e:
        print(f"❌ Error: {e}")
