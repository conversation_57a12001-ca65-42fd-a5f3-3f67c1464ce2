"""
Configuración del Bot de WhatsApp
"""
import os
from typing import Dict, List, Any

class BotConfig:
    """Configuración principal del bot de WhatsApp"""
    
    def __init__(self):
        # Configuración básica
        self.bot_name = "OneForAll WhatsApp Bot"
        self.version = "1.0.0"
        self.debug = True
        
        # Configuración de sesión
        self.session_path = os.path.join(os.path.dirname(__file__), "session")
        self.qr_timeout = 60  # segundos
        
        # Configuración de comandos
        self.command_prefix = "!"
        self.admin_numbers = []  # Lista de números administradores
        
        # Configuración de respuestas automáticas
        self.auto_responses = {
            "hola": "¡Hola! Soy el bot de OneForAll. Escribe !help para ver los comandos disponibles.",
            "help": "Comandos disponibles:\n!ping - Verificar conexión\n!info - Información del bot\n!time - Hora actual",
        }
        
        # Configuración de grupos
        self.allowed_groups = []  # Lista de IDs de grupos permitidos (vacío = todos)
        self.group_commands_enabled = True
        
        # Configuración de logs
        self.log_level = "INFO"
        self.log_file = os.path.join(os.path.dirname(__file__), "logs", "bot.log")
        
        # Configuración de la GUI
        self.gui_enabled = True
        self.gui_theme = "dark"
        
    def is_admin(self, phone_number: str) -> bool:
        """Verifica si un número es administrador"""
        return phone_number in self.admin_numbers
    
    def add_admin(self, phone_number: str):
        """Añade un administrador"""
        if phone_number not in self.admin_numbers:
            self.admin_numbers.append(phone_number)
    
    def remove_admin(self, phone_number: str):
        """Remueve un administrador"""
        if phone_number in self.admin_numbers:
            self.admin_numbers.remove(phone_number)
    
    def get_auto_response(self, message: str) -> str:
        """Obtiene respuesta automática para un mensaje"""
        message_lower = message.lower().strip()
        return self.auto_responses.get(message_lower, None)
    
    def add_auto_response(self, trigger: str, response: str):
        """Añade una respuesta automática"""
        self.auto_responses[trigger.lower()] = response
    
    def remove_auto_response(self, trigger: str):
        """Remueve una respuesta automática"""
        trigger_lower = trigger.lower()
        if trigger_lower in self.auto_responses:
            del self.auto_responses[trigger_lower]

# Instancia global de configuración
config = BotConfig()
