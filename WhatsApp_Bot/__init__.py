"""
Bot de WhatsApp para OneForAll Plugin
"""

from .core.bot import WhatsAppBot
from .core.commands import CommandManager, Command
from .core.logger import <PERSON><PERSON><PERSON><PERSON><PERSON>, bot_logger
from .gui.main_window import WhatsAppBotGUI, main
from .config import config, BotConfig

__version__ = "1.0.0"
__author__ = "OneForAll Plugin"

__all__ = [
    'WhatsAppBot',
    'WhatsAppBotGUI',
    'CommandManager',
    'Command',
    'BotLogger',
    'bot_logger',
    'config',
    'BotConfig',
    'main'
]

# Información del módulo
def get_info():
    """Retorna información del módulo"""
    return {
        "name": "WhatsApp Bot",
        "version": __version__,
        "description": "Bot de WhatsApp integrado con funcionalidades avanzadas",
        "author": __author__,
        "main_class": "WhatsAppBot",
        "gui_class": "WhatsAppBotGUI"
    }