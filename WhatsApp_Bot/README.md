# Bot de WhatsApp - OneForAll Plugin

Un bot completo de WhatsApp con interfaz gráfica y sistema de comandos extensible.

## 🚀 Características

- ✅ Conexión automática a WhatsApp Web
- 🤖 Sistema de comandos extensible
- 💬 Respuestas automáticas personalizables
- 👥 Gestión de grupos y usuarios
- 🖥️ Interfaz gráfica intuitiva
- 📝 Sistema de logs completo
- ⚙️ Configuración flexible
- 🔐 Sistema de administradores

## 📋 Requisitos

- Python 3.8 o superior
- Google Chrome instalado
- Conexión a internet
- Cuenta de WhatsApp

## 🛠️ Instalación

1. **Instalar dependencias:**
   ```bash
   >>> pip install -r requirements.txt
   ```

2. **Configurar el bot:**
   ```python
   >>> from WhatsApp_Bot import config
   >>> config.bot_name = "Mi Bot"
   >>> config.add_admin("tu_numero_aqui")
   ```

## 🎯 Uso Rápido

### Con Interfaz Gráfica
```python
>>> from WhatsApp_Bot import main
>>> main()  # Inicia la GUI
```

### Uso Programático
```python
>>> from WhatsApp_Bot import WhatsAppBot
>>> 
>>> bot = WhatsAppBot()
>>> bot.connect()  # Conectar a WhatsApp Web
>>> bot.start_monitoring()  # Iniciar monitoreo
```

## 📱 Comandos Disponibles

| Comando | Descripción | Ejemplo |
|---------|-------------|---------|
| `!ping` | Verifica conexión | `!ping` |
| `!info` | Información del bot | `!info` |
| `!time` | Hora actual | `!time` |
| `!help` | Lista de comandos | `!help` |
| `!status` | Estado del bot (admin) | `!status` |

## ⚙️ Configuración

### Configuración Básica
```python
>>> from WhatsApp_Bot import config
>>> 
>>> # Configurar nombre del bot
>>> config.bot_name = "Mi Bot Personal"
>>> 
>>> # Cambiar prefijo de comandos
>>> config.command_prefix = "/"
>>> 
>>> # Añadir administradores
>>> config.add_admin("1234567890")
>>> 
>>> # Habilitar debug
>>> config.debug = True
```

### Respuestas Automáticas
```python
>>> # Añadir respuesta automática
>>> config.add_auto_response("hola", "¡Hola! ¿Cómo estás?")
>>> config.add_auto_response("gracias", "¡De nada! 😊")
>>> 
>>> # Remover respuesta
>>> config.remove_auto_response("hola")
```

## 🔧 Comandos Personalizados

### Registrar Comando Simple
```python
>>> def mi_comando(args, sender, chat_id, is_group):
>>>     return f"¡Hola {sender}!"
>>> 
>>> bot.command_manager.register_command(
>>>     "saludo", 
>>>     "Comando de saludo", 
>>>     mi_comando
>>> )
```

### Comando Solo para Administradores
```python
>>> def comando_admin(args, sender, chat_id, is_group):
>>>     return "¡Comando ejecutado por admin!"
>>> 
>>> bot.command_manager.register_command(
>>>     "admin_cmd", 
>>>     "Comando de administrador", 
>>>     comando_admin,
>>>     admin_only=True
>>> )
```

## 📝 Manejadores de Mensajes

```python
>>> def mi_manejador(mensaje, remitente, chat_id, es_grupo):
>>>     if "python" in mensaje.lower():
>>>         bot.send_message("¡Python es genial! 🐍")
>>> 
>>> bot.add_message_handler("python_handler", mi_manejador)
```

## 🖥️ Interfaz Gráfica

La GUI incluye:
- Panel de control (conectar/desconectar)
- Monitor de estado en tiempo real
- Visualizador de logs
- Configuración rápida
- Estadísticas del bot

## 📊 Logs

Los logs se guardan automáticamente en:
- Archivo: `logs/bot.log`
- Consola: Salida estándar
- GUI: Panel de logs integrado

### Niveles de Log
- `INFO`: Información general
- `WARNING`: Advertencias
- `ERROR`: Errores
- `DEBUG`: Información de depuración

## 🔒 Seguridad

### Administradores
```python
>>> # Verificar si es admin
>>> config.is_admin("1234567890")
>>> 
>>> # Añadir admin
>>> config.add_admin("9876543210")
>>> 
>>> # Remover admin
>>> config.remove_admin("1234567890")
```

### Grupos
```python
>>> # Habilitar/deshabilitar comandos en grupos
>>> config.group_commands_enabled = False
>>> 
>>> # Lista de grupos permitidos (vacío = todos)
>>> config.allowed_groups = ["grupo1", "grupo2"]
```

## 🚨 Solución de Problemas

### Error de Conexión
1. Verificar que Chrome esté instalado
2. Comprobar conexión a internet
3. Revisar logs para más detalles

### Bot No Responde
1. Verificar que el monitoreo esté activo
2. Comprobar prefijo de comandos
3. Revisar permisos de administrador

### Problemas con QR
1. Aumentar timeout: `config.qr_timeout = 120`
2. Limpiar sesión: eliminar carpeta `session/`
3. Reiniciar el navegador

## 📁 Estructura del Proyecto

```
WhatsApp_Bot/
├── core/
│   ├── __init__.py
│   ├── bot.py          # Núcleo principal
│   ├── commands.py     # Sistema de comandos
│   └── logger.py       # Sistema de logs
├── gui/
│   ├── __init__.py
│   └── main_window.py  # Interfaz gráfica
├── logs/               # Archivos de log
├── session/            # Datos de sesión
├── config.py           # Configuración
├── info.yaml          # Información del módulo
├── requirements.txt    # Dependencias
├── ejemplo_uso.py     # Ejemplos de uso
└── README.md          # Este archivo
```

## 🤝 Contribuir

1. Fork el proyecto
2. Crea una rama para tu feature
3. Commit tus cambios
4. Push a la rama
5. Abre un Pull Request

## 📄 Licencia

Este proyecto está bajo la licencia MIT.

## 🆘 Soporte

Para soporte y preguntas:
- Crear un issue en GitHub
- Revisar la documentación
- Consultar los ejemplos incluidos

---

**Desarrollado por OneForAll Plugin** 🚀
