from os import listdir
from os.path import isfile, getsize, exists

from OFA_Crypto import bytes2human, shaFile


class OFA_TreeDir:
    def __init__(self, url):
        """:param url = str(Carpeta real), dict(Virtual)"""
        self.url = url
        self.Virtual = False
        self.UrlActual = url
        if isinstance(url, dict):
            self.UrlActual = "./"
            self.Virtual = True
            self.Dict = url
            self.Iterador = self.getNextFile()
            self.Size = None
        if "\\" != self.UrlActual[-1] != "/":
            self.UrlActual += "/"
        self.root = self.UrlActual

    def __repr__(self):
        return f'<OFA_TreeDir UrlActual="{self.UrlActual}", Virtual="{self.Virtual}">'

    def __sub__(self, other):
        # if not self.Virtual:
        #     self.Dict=self.getDict()
        if isinstance(other,OFA_TreeDir):
            other=other.getDict()
        return self.CheckSub(other)

    def __iter__(self):
        return self.getNextFile()

    def CheckSub(self,Other,Dic=None, DicError=None):
        # si no esta en Other lo tengo que devolver
        if DicError is None:
            DicError = {}
        if Dic is None:
            Dic=self.Dict
            if Other == Dic:return {}
        for i, sha in Dic.items():
            if i in Other:
                if sha != Other[i]:
                    if isinstance(sha,dict):
                        DicError[i] = {}
                        if not self.CheckSub(Other[i], Dic[i], DicError[i]):
                            DicError.pop(i)
                    else:DicError[i] = sha
            else:DicError[i] = sha
        return DicError

    def getNextFile(self,DIC=None,ruta=None):
        if DIC is None:DIC=self.Dict
        if ruta is None:ruta='.'
        for c,v in DIC.items():
            if isinstance(v,dict):
                yield from self.getNextFile(v,ruta+'/'+c)
            else:yield ruta+'/'+c,v

    def next(self):
        if self.Virtual:
            try:
                return self.Iterador.__next__()
            except:
                self.Iterador = self.getNextFile()
                return None, None
        else:return None,None

    def Exist(self,filedir):
        filedir=filedir.replace('\\','/')
        filedir=filedir.split('/')
        if filedir[0]=='.':filedir.pop(0)
        x=self.Dict
        while filedir:
            a=filedir.pop(0)
            if a in x:
                x=x[a]
            else:return None
        return True

    def show(self, url=None, espace="", Dict=None):
        if not url:
            url = self.UrlActual
            print(">" + url)
        espace += "|   "
        if not self.Virtual:
            for file in listdir(url):
                if isfile(url+"/"+file):
                    size = getsize(url+"/"+file)
                    print(espace, file, "- %s %s" %bytes2human(size, 1))
                else:
                    print(espace+">", file, "- ?")
                    try:self.show(url + file + "/", espace)
                    except:print(espace + "^^", "Error")
        else:
            if Dict is None:
                Dict = self.Dict
            for file, data in Dict.items():
                if isinstance(data, dict):
                    print(espace+">", file, "- ?")
                    self.show(url + file + "/", espace, Dict[file])
                elif data is None:
                    print(espace + "^^", "Error")
                elif isinstance(data, tuple):
                    print(espace, file, "- %s %s" %bytes2human(data[1], 1))
                else:
                    print(espace, file, "- ? B")

    def getDict(self, url=None, virtuality=None,filtro=None,getTime=False):
        if filtro is None:filtro = []
        if url is None:url = self.root
        if virtuality is None:virtuality = {}
        if not self.Virtual:
            for file in listdir(url):
                if not file in filtro:
                    if isfile(url + "/" + file):
                        virtuality[file] = shaFile(url + "/" + file,getTime)
                    else:
                        virtuality[file] = {}
                        try:self.getDict(url + file + "/", virtuality[file], filtro,getTime)
                        except:virtuality[file] = None
        else:virtuality = self.Dict.copy()
        return virtuality

    def listDir(self, url=None):
        if url is None:
            url = self.root
        carpeta = self.Dict
        lista = []
        if self.Virtual:
            urlList = url.split("/")[1:]
            for i in urlList:
                if i:
                    if i in carpeta:
                        carpeta = carpeta[i]
                    else:
                        raise ("Direccion no encontrada.")
            for i in carpeta:
                lista.append(i)
        else:
            lista = listdir(url)
        return lista

    def remove(self, url):
        if self.Virtual:
            urlList = url.split("/")[1:]
            delurlList = urlList[-1]
            urlList = urlList[:-1]
            if not len(urlList) and not delurlList:
                self.Dict = {}
                return
            carpeta = self.Dict
            for i in urlList:
                if i in carpeta:
                    carpeta = carpeta[i]
                else:raise ("Direccion no encontrada.")
            if delurlList:
                if delurlList in carpeta:
                    carpeta.pop(delurlList)
                else:raise ("Direccion no encontrada.")

    def Check(self,Ruta,Dic=None, DicError=None, FilesGood=None,getTime=False):
        if DicError is None:DicError = {}
        if FilesGood is None:FilesGood = {}
        if Dic is None:Dic=self.Dict
        for i, sha in Dic.items():
            if exists(Ruta+"/"+i):
                if isfile(Ruta+"/"+i):
                    if shaFile(Ruta+"/"+i,getTime) != sha:
                        DicError[i] = sha
                    else:
                        FilesGood[i] = sha
                else:
                    DicError[i] = {}
                    FilesGood[i] = {}
                    files = self.Check(Ruta+"/"+i, Dic[i], DicError[i], FilesGood[i],getTime)
                    if not files[0]:
                        DicError.pop(i)
                    if not files[1]:
                        FilesGood.pop(i)
            else:
                DicError[i] = sha
        return DicError, FilesGood

    def getSize(self, Dict=None):
        if self.Size is None or not Dict is None:
            if Dict is None:
                Dict = self.Dict
                self.Size = 0
            for file, data in Dict.items():
                if isinstance(data, dict):
                    self.getSize(Dict[file])
                elif isinstance(data, tuple):
                    self.Size += data[1]
        return self.Size

    def AddDir(self, url, virtuality=None, filtro=None,getTime=False):
        if filtro is None: filtro = []
        if virtuality is None: virtuality = {}
        if not isinstance(url,dict):
            url = url.replace('\\', '/')
            for file in listdir(url):
                if not file in filtro:
                    if isfile(url + "/" + file):
                        virtuality[file] = shaFile(url + "/" + file,getTime)
                    else:
                        virtuality[file] = {}
                        try:
                            self.AddDir(url + file + "/", virtuality[file], filtro)
                        except:
                            virtuality[file] = None
        else:
            virtuality=url
        self.Dict=self+virtuality
        return self.Dict

    def __add__(self, other):
        if isinstance(other, OFA_TreeDir):
            other = other.getDict()
        return self.CheckAdd(other)

    def CheckAdd(self, Other, Dic=None, DicResult=None):
        # si no esta en Other lo tengo que devolver
        if DicResult is None:
            if  isinstance(Other, dict):DicResult=Other
            else:DicResult = {}
        if Dic is None:
            Dic = self.Dict
            if Other == Dic: return Other
        for i, sha in Dic.items():
            if i in Other:
                if sha != Other[i] and isinstance(sha, dict):
                    self.CheckAdd(Other[i], Dic[i], DicResult[i])
            else:
                DicResult[i] = sha
        return DicResult


if __name__ == '__main__':
    urlReal = OFA_TreeDir("C:\\H\\Proyectaos\\_OneForAll\\dist\\One For All/")
    print(urlReal)
    # urlReal.show()
    # print("Raiz", urlReal.listDir())
    # urlVirtual2 = OFA_TreeDir(urlReal.getDict())
    urlVirtual = OFA_TreeDir(urlReal.getDict())
    print(urlVirtual)
    # urlVirtual.show()
    # # print(urlVirtual.listDir())
    # urlVirtual.remove("./imagenes/Avatars/Avatar1.gif")
    # DIC1 = urlVirtual
    # # print("./", urlVirtual.listDir())
    # X=urlVirtual2-DIC1
    # print('---',X)
    # print('---',urlVirtual2-X)
    # X = urlVirtual.getNextFile()
    # while True:
    #     input()
    #     print(X)
    #